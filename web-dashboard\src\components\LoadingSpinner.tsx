import React from 'react'
import { Spin, Typography } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

const { Text } = Typography

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  text?: string
  fullScreen?: boolean
  className?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  text = 'جاري التحميل...',
  fullScreen = true,
  className = ''
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 40 : 24 }} spin />

  const spinnerContent = (
    <div className={`loading-container ${className}`}>
      <div style={{ textAlign: 'center' }}>
        <Spin indicator={antIcon} size={size} />
        {text && (
          <div style={{ marginTop: 16 }}>
            <Text type="secondary" style={{ fontSize: 16 }}>
              {text}
            </Text>
          </div>
        )}
      </div>
    </div>
  )

  if (fullScreen) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(255, 255, 255, 0.9)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 9999,
        }}
      >
        {spinnerContent}
      </div>
    )
  }

  return spinnerContent
}

export default LoadingSpinner
