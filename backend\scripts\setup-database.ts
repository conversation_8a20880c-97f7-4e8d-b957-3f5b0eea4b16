#!/usr/bin/env ts-node

import knex from 'knex';
import knexConfig from '../knexfile';
import config from '../src/config';
import logger from '../src/utils/logger';

const db = knex(knexConfig[config.NODE_ENV]);

async function setupDatabase(): Promise<void> {
  try {
    logger.info('🚀 Starting database setup...');

    // Test connection
    logger.info('📡 Testing database connection...');
    await db.raw('SELECT 1');
    logger.info('✅ Database connection successful');

    // Run migrations
    logger.info('📋 Running migrations...');
    const [batchNo, migrations] = await db.migrate.latest();
    
    if (migrations.length === 0) {
      logger.info('📋 No new migrations to run');
    } else {
      logger.info(`📋 Ran ${migrations.length} migrations in batch ${batchNo}:`);
      migrations.forEach(migration => {
        logger.info(`  - ${migration}`);
      });
    }

    // Run seeds
    logger.info('🌱 Running seeds...');
    const seedFiles = await db.seed.run();
    
    if (seedFiles.length === 0) {
      logger.info('🌱 No seed files to run');
    } else {
      logger.info(`🌱 Ran ${seedFiles.length} seed files:`);
      seedFiles.forEach(seedFile => {
        logger.info(`  - ${seedFile}`);
      });
    }

    logger.info('✅ Database setup completed successfully!');

    // Display summary
    logger.info('\n📊 Database Summary:');
    
    const tables = [
      'users',
      'colors', 
      'patterns',
      'prices_config',
      'orders',
      'order_items',
      'system_settings'
    ];

    for (const table of tables) {
      try {
        const count = await db(table).count('* as count').first();
        logger.info(`  - ${table}: ${count?.count || 0} records`);
      } catch (error) {
        logger.warn(`  - ${table}: Error counting records`);
      }
    }

    // Display admin credentials
    const adminUser = await db('users')
      .select('phone_number', 'email')
      .where('role', 'admin')
      .first();

    if (adminUser) {
      logger.info('\n🔐 Admin Credentials:');
      logger.info(`  📱 Phone: ${adminUser.phone_number}`);
      logger.info(`  📧 Email: ${adminUser.email}`);
      logger.info(`  🔑 Password: admin123456`);
    }

    logger.info('\n🎉 Ready to start the application!');
    logger.info('Run: npm run dev');

  } catch (error) {
    logger.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await db.destroy();
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Database Setup Script

Usage: npm run db:setup [options]

Options:
  --fresh    Drop all tables and recreate (destructive)
  --help     Show this help message

Examples:
  npm run db:setup          # Run migrations and seeds
  npm run db:setup --fresh  # Drop everything and recreate
  `);
  process.exit(0);
}

if (args.includes('--fresh')) {
  logger.warn('⚠️ Running in FRESH mode - all data will be lost!');
  
  // Add confirmation in production
  if (config.NODE_ENV === 'production') {
    logger.error('❌ Fresh mode is not allowed in production!');
    process.exit(1);
  }

  // Rollback all migrations first
  db.migrate.rollback({ all: true })
    .then(() => {
      logger.info('🗑️ All migrations rolled back');
      return setupDatabase();
    })
    .catch(error => {
      logger.error('❌ Rollback failed:', error);
      process.exit(1);
    });
} else {
  setupDatabase();
}

// Handle process termination
process.on('SIGINT', async () => {
  logger.info('🛑 Received SIGINT, closing database connection...');
  await db.destroy();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('🛑 Received SIGTERM, closing database connection...');
  await db.destroy();
  process.exit(0);
});
