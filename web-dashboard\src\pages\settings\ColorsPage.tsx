import React from 'react'
import { Card, Typography, Result } from 'antd'
import { Helmet } from 'react-helmet-async'

const { Title } = Typography

const ColorsPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>إدارة الألوان - معمل الشمس</title>
      </Helmet>

      <div>
        <Title level={2}>إدارة الألوان</Title>
        
        <Card>
          <Result
            status="info"
            title="صفحة قيد التطوير"
            subTitle="صفحة إدارة الألوان ستكون متاحة قريباً"
          />
        </Card>
      </div>
    </>
  )
}

export default ColorsPage
