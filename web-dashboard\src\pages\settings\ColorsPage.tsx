import React, { useState } from 'react'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  Row,
  Col,
  Switch,
  message,
  Popconfirm,
  ColorPicker,
  Image
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  BgColorsOutlined,
  UploadOutlined
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'

const { Title } = Typography

interface Color {
  key: string
  id: string
  name: string
  name_ar: string
  hex_code: string
  image_url?: string
  is_active: boolean
  created_at: string
}

const ColorsPage: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [modalVisible, setModalVisible] = useState(false)
  const [editingColor, setEditingColor] = useState<Color | null>(null)
  const [form] = Form.useForm()

  // بيانات تجريبية للألوان
  const [colors, setColors] = useState<Color[]>([
    {
      key: '1',
      id: '1',
      name: 'White',
      name_ar: 'أبيض',
      hex_code: '#FFFFFF',
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '2',
      id: '2',
      name: 'Cream',
      name_ar: 'كريمي',
      hex_code: '#F5F5DC',
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '3',
      id: '3',
      name: 'Light Brown',
      name_ar: 'بني فاتح',
      hex_code: '#D2B48C',
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '4',
      id: '4',
      name: 'Brown',
      name_ar: 'بني',
      hex_code: '#8B4513',
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '5',
      id: '5',
      name: 'Dark Brown',
      name_ar: 'بني داكن',
      hex_code: '#654321',
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '6',
      id: '6',
      name: 'Black',
      name_ar: 'أسود',
      hex_code: '#000000',
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '7',
      id: '7',
      name: 'Gray',
      name_ar: 'رمادي',
      hex_code: '#808080',
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '8',
      id: '8',
      name: 'Light Gray',
      name_ar: 'رمادي فاتح',
      hex_code: '#D3D3D3',
      is_active: false,
      created_at: '2024-01-01'
    }
  ])

  const handleAddColor = () => {
    setEditingColor(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEditColor = (color: Color) => {
    setEditingColor(color)
    form.setFieldsValue(color)
    setModalVisible(true)
  }

  const handleDeleteColor = (colorId: string) => {
    setColors(colors.filter(color => color.id !== colorId))
    message.success('تم حذف اللون بنجاح')
  }

  const handleToggleStatus = (colorId: string) => {
    setColors(colors.map(color =>
      color.id === colorId
        ? { ...color, is_active: !color.is_active }
        : color
    ))
    message.success('تم تحديث حالة اللون')
  }

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      if (editingColor) {
        // تحديث لون موجود
        setColors(colors.map(color =>
          color.id === editingColor.id
            ? { ...color, ...values }
            : color
        ))
        message.success('تم تحديث اللون بنجاح')
      } else {
        // إضافة لون جديد
        const newColor: Color = {
          ...values,
          key: Date.now().toString(),
          id: Date.now().toString(),
          created_at: new Date().toISOString().split('T')[0],
          is_active: true
        }
        setColors([...colors, newColor])
        message.success('تم إضافة اللون بنجاح')
      }

      setModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      title: 'اللون',
      key: 'color',
      render: (record: Color) => (
        <Space>
          <div
            style={{
              width: 40,
              height: 40,
              backgroundColor: record.hex_code,
              border: '2px solid #d9d9d9',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {record.hex_code === '#FFFFFF' && (
              <div style={{ fontSize: 12, color: '#999' }}>⚪</div>
            )}
          </div>
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name_ar}</div>
            <div style={{ fontSize: 12, color: '#666' }}>
              {record.name}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: 'كود اللون',
      dataIndex: 'hex_code',
      key: 'hex_code',
      render: (hex_code: string) => (
        <span
          style={{
            fontFamily: 'monospace',
            backgroundColor: '#f5f5f5',
            padding: '2px 6px',
            borderRadius: '4px'
          }}
        >
          {hex_code}
        </span>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (is_active: boolean, record: Color) => (
        <Switch
          checked={is_active}
          onChange={() => handleToggleStatus(record.id)}
          checkedChildren="مفعل"
          unCheckedChildren="معطل"
        />
      )
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: Color) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditColor(record)}
          >
            تعديل
          </Button>
          <Popconfirm
            title="هل أنت متأكد من حذف هذا اللون؟"
            description="سيؤثر هذا على جميع الطلبات المرتبطة بهذا اللون"
            onConfirm={() => handleDeleteColor(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              حذف
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  const filteredColors = colors.filter(color => {
    const matchesSearch = !searchText ||
      color.name.toLowerCase().includes(searchText.toLowerCase()) ||
      color.name_ar.toLowerCase().includes(searchText.toLowerCase()) ||
      color.hex_code.toLowerCase().includes(searchText.toLowerCase())

    return matchesSearch
  })

  return (
    <>
      <Helmet>
        <title>إدارة الألوان - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={2} style={{ margin: 0 }}>
              إدارة الألوان
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddColor}
            >
              لون جديد
            </Button>
          </div>

          {/* Search */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={12}>
                <Input
                  placeholder="البحث في الألوان..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
            </Row>
          </Card>
        </div>

        <Card>
          <Table
            columns={columns}
            dataSource={filteredColors}
            loading={loading}
            pagination={{
              total: filteredColors.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} من ${total} لون`
            }}
          />
        </Card>

        {/* Modal for Add/Edit Color */}
        <Modal
          title={editingColor ? 'تعديل اللون' : 'إضافة لون جديد'}
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={null}
          width={500}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Row gutter={16}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="name_ar"
                  label="الاسم بالعربية"
                  rules={[{ required: true, message: 'يرجى إدخال الاسم بالعربية' }]}
                >
                  <Input
                    placeholder="أدخل الاسم بالعربية"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="name"
                  label="الاسم بالإنجليزية"
                  rules={[{ required: true, message: 'يرجى إدخال الاسم بالإنجليزية' }]}
                >
                  <Input
                    placeholder="Enter English name"
                    dir="ltr"
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item
                  name="hex_code"
                  label="كود اللون"
                  rules={[
                    { required: true, message: 'يرجى اختيار اللون' },
                    { pattern: /^#[0-9A-F]{6}$/i, message: 'كود اللون غير صحيح' }
                  ]}
                >
                  <Input
                    placeholder="#FFFFFF"
                    dir="ltr"
                    addonBefore={
                      <Form.Item noStyle shouldUpdate>
                        {() => {
                          const hexCode = form.getFieldValue('hex_code')
                          return (
                            <ColorPicker
                              value={hexCode}
                              onChange={(color) => {
                                form.setFieldValue('hex_code', color.toHexString().toUpperCase())
                              }}
                              showText={false}
                              size="small"
                            />
                          )
                        }}
                      </Form.Item>
                    }
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item
                  name="image_url"
                  label="رابط الصورة (اختياري)"
                >
                  <Input
                    placeholder="https://example.com/image.jpg"
                    dir="ltr"
                    addonAfter={<UploadOutlined />}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Preview */}
            <Form.Item noStyle shouldUpdate>
              {() => {
                const hexCode = form.getFieldValue('hex_code')
                const nameAr = form.getFieldValue('name_ar')
                const name = form.getFieldValue('name')

                if (hexCode && nameAr) {
                  return (
                    <Card
                      type="inner"
                      title="معاينة اللون"
                      style={{ marginBottom: 16 }}
                    >
                      <Space>
                        <div
                          style={{
                            width: 60,
                            height: 60,
                            backgroundColor: hexCode,
                            border: '2px solid #d9d9d9',
                            borderRadius: '8px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          {hexCode === '#FFFFFF' && (
                            <div style={{ fontSize: 16, color: '#999' }}>⚪</div>
                          )}
                        </div>
                        <div>
                          <div style={{ fontWeight: 'bold', fontSize: 16 }}>{nameAr}</div>
                          <div style={{ color: '#666' }}>{name}</div>
                          <div style={{
                            fontFamily: 'monospace',
                            backgroundColor: '#f5f5f5',
                            padding: '2px 6px',
                            borderRadius: '4px',
                            fontSize: 12
                          }}>
                            {hexCode}
                          </div>
                        </div>
                      </Space>
                    </Card>
                  )
                }
                return null
              }}
            </Form.Item>

            <div style={{ textAlign: 'left', marginTop: 24 }}>
              <Space>
                <Button onClick={() => setModalVisible(false)}>
                  إلغاء
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<BgColorsOutlined />}
                >
                  {editingColor ? 'تحديث' : 'إضافة'}
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>
      </div>
    </>
  )
}

export default ColorsPage
