import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'
import { UserRole } from '../types'
import LoadingSpinner from './LoadingSpinner'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRoles?: UserRole[]
  fallbackPath?: string
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles = [],
  fallbackPath = '/auth/login'
}) => {
  const { isAuthenticated, isLoading, user } = useAuthStore()
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner text="جاري التحقق من الصلاحيات..." />
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location }} 
        replace 
      />
    )
  }

  // Check role-based access if required roles are specified
  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.includes(user.role)
    
    if (!hasRequiredRole) {
      // Redirect to dashboard or show unauthorized page
      return (
        <Navigate 
          to="/dashboard" 
          state={{ 
            from: location,
            error: 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
          }} 
          replace 
        />
      )
    }
  }

  // Check if user account is active
  if (user && !user.is_active) {
    return (
      <Navigate 
        to="/auth/login" 
        state={{ 
          from: location,
          error: 'حسابك غير مفعل، يرجى التواصل مع الإدارة'
        }} 
        replace 
      />
    )
  }

  return <>{children}</>
}

export default ProtectedRoute
