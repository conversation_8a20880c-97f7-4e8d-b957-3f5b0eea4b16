import React, { useState } from 'react'
import {
  Card,
  Typography,
  Form,
  Input,
  Button,
  Row,
  Col,
  Avatar,
  Upload,
  message,
  Divider,
  Space,
  Tag,
  Descriptions,
  Modal
} from 'antd'
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  LockOutlined,
  CameraOutlined,
  EditOutlined,
  SaveOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'
import { useAuthStore, useUser } from '../../stores/authStore'
import { UserRole } from '../../types'

const { Title, Text } = Typography

const ProfilePage: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [passwordModalVisible, setPasswordModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const { updateUser } = useAuthStore()
  const user = useUser()

  const getRoleText = (role: UserRole) => {
    const roleMap = {
      [UserRole.ADMIN]: 'مدير النظام',
      [UserRole.CLIENT]: 'عميل',
      [UserRole.MEASUREMENT]: 'فني قياسات',
      [UserRole.DESIGNER]: 'مصمم',
      [UserRole.CUTTING]: 'فني تفصال',
      [UserRole.PRESSING]: 'فني كبس',
      [UserRole.VACUUM]: 'فني فاكيوم',
      [UserRole.EDGE_CUTTING]: 'فني حواف',
      [UserRole.TAPE_APPLICATION]: 'فني شريط',
      [UserRole.PACKAGING]: 'فني تغليف',
      [UserRole.DELIVERY]: 'فني توصيل'
    }
    return roleMap[role] || role
  }

  const getRoleColor = (role: UserRole) => {
    const colorMap = {
      [UserRole.ADMIN]: 'red',
      [UserRole.CLIENT]: 'blue',
      [UserRole.MEASUREMENT]: 'orange',
      [UserRole.DESIGNER]: 'purple',
      [UserRole.CUTTING]: 'cyan',
      [UserRole.PRESSING]: 'magenta',
      [UserRole.VACUUM]: 'lime',
      [UserRole.EDGE_CUTTING]: 'gold',
      [UserRole.TAPE_APPLICATION]: 'volcano',
      [UserRole.PACKAGING]: 'geekblue',
      [UserRole.DELIVERY]: 'green'
    }
    return colorMap[role] || 'default'
  }

  const handleUpdateProfile = async (values: any) => {
    try {
      setLoading(true)

      // هنا سيتم إرسال البيانات للـ API
      updateUser(values)

      message.success('تم تحديث الملف الشخصي بنجاح')
      setEditMode(false)
    } catch (error) {
      message.error('حدث خطأ أثناء تحديث الملف الشخصي')
    } finally {
      setLoading(false)
    }
  }

  const handleChangePassword = async (values: any) => {
    try {
      setLoading(true)

      // هنا سيتم إرسال البيانات للـ API
      console.log('Changing password:', values)

      message.success('تم تغيير كلمة المرور بنجاح')
      setPasswordModalVisible(false)
      passwordForm.resetFields()
    } catch (error) {
      message.error('حدث خطأ أثناء تغيير كلمة المرور')
    } finally {
      setLoading(false)
    }
  }

  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success('تم رفع الصورة بنجاح')
    } else if (info.file.status === 'error') {
      message.error('فشل في رفع الصورة')
    }
  }

  if (!user) {
    return <div>جاري التحميل...</div>
  }

  return (
    <>
      <Helmet>
        <title>الملف الشخصي - معمل الشمس</title>
      </Helmet>

      <div>
        <Title level={2}>الملف الشخصي</Title>

        <Row gutter={[16, 16]}>
          {/* Profile Card */}
          <Col xs={24} lg={8}>
            <Card>
              <div style={{ textAlign: 'center', marginBottom: 24 }}>
                <Upload
                  name="avatar"
                  listType="picture-card"
                  className="avatar-uploader"
                  showUploadList={false}
                  action="/api/v1/upload/avatar"
                  onChange={handleAvatarUpload}
                >
                  <Avatar
                    size={100}
                    icon={<UserOutlined />}
                    style={{ backgroundColor: '#1890ff' }}
                  />
                  <div style={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    backgroundColor: '#1890ff',
                    borderRadius: '50%',
                    width: 32,
                    height: 32,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer'
                  }}>
                    <CameraOutlined style={{ color: 'white', fontSize: 16 }} />
                  </div>
                </Upload>

                <Title level={4} style={{ marginTop: 16, marginBottom: 8 }}>
                  {user.name}
                </Title>

                <Tag color={getRoleColor(user.role)} style={{ marginBottom: 16 }}>
                  {getRoleText(user.role)}
                </Tag>

                <div style={{ color: '#666' }}>
                  عضو منذ {new Date(user.created_at).getFullYear()}
                </div>
              </div>

              <Descriptions column={1} size="small">
                <Descriptions.Item label="رقم الهاتف">
                  <Text dir="ltr">{user.phone_number}</Text>
                </Descriptions.Item>
                {user.email && (
                  <Descriptions.Item label="البريد الإلكتروني">
                    <Text dir="ltr">{user.email}</Text>
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="الحالة">
                  <Tag color={user.is_active ? 'green' : 'red'}>
                    {user.is_active ? 'مفعل' : 'معطل'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="تاريخ الإنشاء">
                  {new Date(user.created_at).toLocaleDateString('ar-SA')}
                </Descriptions.Item>
                <Descriptions.Item label="آخر تحديث">
                  {new Date(user.updated_at).toLocaleDateString('ar-SA')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          {/* Profile Form */}
          <Col xs={24} lg={16}>
            <Card
              title="معلومات الحساب"
              extra={
                <Space>
                  {!editMode ? (
                    <Button
                      type="primary"
                      icon={<EditOutlined />}
                      onClick={() => {
                        setEditMode(true)
                        form.setFieldsValue(user)
                      }}
                    >
                      تعديل
                    </Button>
                  ) : (
                    <Space>
                      <Button onClick={() => setEditMode(false)}>
                        إلغاء
                      </Button>
                      <Button
                        type="primary"
                        icon={<SaveOutlined />}
                        onClick={() => form.submit()}
                        loading={loading}
                      >
                        حفظ
                      </Button>
                    </Space>
                  )}
                </Space>
              }
            >
              <Form
                form={form}
                layout="vertical"
                onFinish={handleUpdateProfile}
                initialValues={user}
              >
                <Row gutter={16}>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="name"
                      label="الاسم الكامل"
                      rules={[{ required: true, message: 'يرجى إدخال الاسم' }]}
                    >
                      <Input
                        prefix={<UserOutlined />}
                        disabled={!editMode}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="phone_number"
                      label="رقم الهاتف"
                      rules={[{ required: true, message: 'يرجى إدخال رقم الهاتف' }]}
                    >
                      <Input
                        prefix={<PhoneOutlined />}
                        disabled={!editMode}
                        dir="ltr"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="email"
                      label="البريد الإلكتروني"
                      rules={[{ type: 'email', message: 'البريد الإلكتروني غير صحيح' }]}
                    >
                      <Input
                        prefix={<MailOutlined />}
                        disabled={!editMode}
                        dir="ltr"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      label="الدور"
                    >
                      <Input
                        value={getRoleText(user.role)}
                        disabled
                        addonBefore={
                          <Tag color={getRoleColor(user.role)}>
                            {user.role}
                          </Tag>
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </Card>

            <Card
              title="الأمان"
              style={{ marginTop: 16 }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text strong>كلمة المرور</Text>
                    <br />
                    <Text type="secondary">آخر تغيير منذ 30 يوماً</Text>
                  </div>
                  <Button
                    icon={<LockOutlined />}
                    onClick={() => setPasswordModalVisible(true)}
                  >
                    تغيير كلمة المرور
                  </Button>
                </div>

                <Divider />

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text strong>المصادقة الثنائية</Text>
                    <br />
                    <Text type="secondary">حماية إضافية لحسابك</Text>
                  </div>
                  <Button disabled>
                    قريباً
                  </Button>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* Change Password Modal */}
        <Modal
          title="تغيير كلمة المرور"
          open={passwordModalVisible}
          onCancel={() => setPasswordModalVisible(false)}
          footer={null}
          width={400}
        >
          <Form
            form={passwordForm}
            layout="vertical"
            onFinish={handleChangePassword}
          >
            <Form.Item
              name="current_password"
              label="كلمة المرور الحالية"
              rules={[{ required: true, message: 'يرجى إدخال كلمة المرور الحالية' }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item
              name="new_password"
              label="كلمة المرور الجديدة"
              rules={[
                { required: true, message: 'يرجى إدخال كلمة المرور الجديدة' },
                { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item
              name="confirm_password"
              label="تأكيد كلمة المرور"
              dependencies={['new_password']}
              rules={[
                { required: true, message: 'يرجى تأكيد كلمة المرور' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('new_password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('كلمة المرور غير متطابقة'))
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <div style={{ textAlign: 'left', marginTop: 24 }}>
              <Space>
                <Button onClick={() => setPasswordModalVisible(false)}>
                  إلغاء
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                >
                  تغيير كلمة المرور
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>
      </div>
    </>
  )
}

export default ProfilePage
