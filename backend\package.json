{"name": "@alshams/backend", "version": "1.0.0", "description": "Backend API for Al Shams Door Factory Management System", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "migrate:make": "knex migrate:make", "seed": "knex seed:run", "seed:make": "knex seed:make", "db:reset": "npm run migrate:rollback && npm run migrate && npm run seed", "db:setup": "ts-node scripts/setup-database.ts", "db:fresh": "ts-node scripts/setup-database.ts --fresh", "db:status": "knex migrate:status", "cleanup": "ts-node scripts/cleanup.ts"}, "keywords": ["nodejs", "express", "typescript", "postgresql", "api"], "author": "Al Shams Door Factory", "license": "PROPRIETARY", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "uuid": "^9.0.0", "dotenv": "^16.3.1", "pg": "^8.11.1", "knex": "^2.5.1", "redis": "^4.6.7", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "nodemailer": "^6.9.3", "twilio": "^4.14.0", "firebase-admin": "^11.9.0", "aws-sdk": "^2.1406.0", "joi": "^17.9.2", "winston": "^3.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tsconfig-paths": "^4.2.0"}, "devDependencies": {"@types/node": "^20.4.2", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/compression": "^1.7.2", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/uuid": "^9.0.2", "@types/pg": "^8.10.2", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.8", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/jest": "^29.5.3", "@types/supertest": "^2.0.12", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.1", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}