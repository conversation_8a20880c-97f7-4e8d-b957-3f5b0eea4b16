import React from 'react'
import { Outlet } from 'react-router-dom'
import { Layout, Card, Typography, Space, Divider } from 'antd'
import { ShopOutlined, SafetyOutlined, CustomerServiceOutlined } from '@ant-design/icons'

const { Content } = Layout
const { Title, Text, Paragraph } = Typography

const AuthLayout: React.FC = () => {
  return (
    <Layout style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
      <Content style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        padding: '20px'
      }}>
        <div style={{ 
          display: 'flex', 
          maxWidth: 1200, 
          width: '100%',
          gap: '40px',
          alignItems: 'center'
        }}>
          {/* Left side - Branding */}
          <div style={{ 
            flex: 1, 
            color: 'white',
            textAlign: 'center',
            display: window.innerWidth > 768 ? 'block' : 'none'
          }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <ShopOutlined style={{ fontSize: 80, marginBottom: 20 }} />
                <Title level={1} style={{ color: 'white', margin: 0 }}>
                  معمل الشمس
                </Title>
                <Title level={3} style={{ color: 'rgba(255,255,255,0.9)', margin: 0 }}>
                  لصناعة الأبواب
                </Title>
              </div>
              
              <Paragraph style={{ 
                color: 'rgba(255,255,255,0.8)', 
                fontSize: 18,
                lineHeight: 1.6
              }}>
                نظام إدارة شامل لمعمل صناعة الأبواب يوفر متابعة دقيقة لجميع مراحل الإنتاج
                من استلام الطلب حتى التسليم النهائي
              </Paragraph>

              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 15 }}>
                  <SafetyOutlined style={{ fontSize: 24 }} />
                  <Text style={{ color: 'white', fontSize: 16 }}>
                    نظام آمن ومحمي
                  </Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 15 }}>
                  <CustomerServiceOutlined style={{ fontSize: 24 }} />
                  <Text style={{ color: 'white', fontSize: 16 }}>
                    دعم فني متواصل
                  </Text>
                </div>
              </Space>
            </Space>
          </div>

          {/* Right side - Auth Form */}
          <div style={{ 
            flex: window.innerWidth > 768 ? 1 : 'none',
            maxWidth: 400,
            width: '100%'
          }}>
            <Card
              style={{
                borderRadius: 12,
                boxShadow: '0 10px 40px rgba(0,0,0,0.2)',
                border: 'none'
              }}
              bodyStyle={{ padding: '40px 30px' }}
            >
              <div style={{ textAlign: 'center', marginBottom: 30 }}>
                <ShopOutlined style={{ 
                  fontSize: 48, 
                  color: '#1890ff',
                  marginBottom: 16
                }} />
                <Title level={3} style={{ margin: 0, color: '#333' }}>
                  تسجيل الدخول
                </Title>
                <Text type="secondary">
                  أدخل بياناتك للوصول إلى لوحة التحكم
                </Text>
              </div>

              <Outlet />

              <Divider />

              <div style={{ textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  © 2024 معمل الشمس لصناعة الأبواب. جميع الحقوق محفوظة.
                </Text>
              </div>
            </Card>
          </div>
        </div>
      </Content>
    </Layout>
  )
}

export default AuthLayout
