#!/usr/bin/env ts-node

import knex from 'knex';
import knexConfig from '../knexfile';
import config from '../src/config';
import logger from '../src/utils/logger';

const db = knex(knexConfig[config.NODE_ENV]);

async function cleanupDatabase(): Promise<void> {
  try {
    logger.info('🧹 Starting database cleanup...');

    // Clean up expired OTP codes
    logger.info('🔑 Cleaning up expired OTP codes...');
    const expiredOtpCount = await db('otp_codes')
      .where('expires_at', '<', new Date())
      .orWhere(function() {
        this.where('is_used', true)
            .andWhere('created_at', '<', db.raw("CURRENT_TIMESTAMP - INTERVAL '1 hour'"));
      })
      .del();
    
    logger.info(`🔑 Deleted ${expiredOtpCount} expired OTP codes`);

    // Clean up expired sessions
    logger.info('🔐 Cleaning up expired sessions...');
    const expiredSessionsCount = await db('sessions')
      .where('expires_at', '<', new Date())
      .del();
    
    logger.info(`🔐 Deleted ${expiredSessionsCount} expired sessions`);

    // Clean up old audit logs (keep last 90 days)
    logger.info('📋 Cleaning up old audit logs...');
    const oldAuditLogsCount = await db('audit_logs')
      .where('created_at', '<', db.raw("CURRENT_TIMESTAMP - INTERVAL '90 days'"))
      .del();
    
    logger.info(`📋 Deleted ${oldAuditLogsCount} old audit log entries`);

    // Clean up orphaned file uploads (files not referenced by any record)
    logger.info('📁 Cleaning up orphaned file uploads...');
    
    // Get all file uploads with related records
    const orphanedFiles = await db('file_uploads as f')
      .leftJoin('orders as o', function() {
        this.on('f.related_table', '=', db.raw("'orders'"))
            .andOn('f.related_id', '=', 'o.id');
      })
      .leftJoin('order_items as oi', function() {
        this.on('f.related_table', '=', db.raw("'order_items'"))
            .andOn('f.related_id', '=', 'oi.id');
      })
      .leftJoin('users as u', function() {
        this.on('f.related_table', '=', db.raw("'users'"))
            .andOn('f.related_id', '=', 'u.id');
      })
      .whereNull('o.id')
      .whereNull('oi.id')
      .whereNull('u.id')
      .where('f.created_at', '<', db.raw("CURRENT_TIMESTAMP - INTERVAL '7 days'"))
      .select('f.id', 'f.filename', 'f.url');

    if (orphanedFiles.length > 0) {
      const orphanedFileIds = orphanedFiles.map(f => f.id);
      await db('file_uploads').whereIn('id', orphanedFileIds).del();
      logger.info(`📁 Deleted ${orphanedFiles.length} orphaned file uploads`);
      
      // Log files that should be deleted from storage
      logger.info('📁 Files to delete from storage:');
      orphanedFiles.forEach(file => {
        logger.info(`  - ${file.filename} (${file.url})`);
      });
    } else {
      logger.info('📁 No orphaned file uploads found');
    }

    // Clean up old notifications (keep last 30 days for read notifications)
    logger.info('🔔 Cleaning up old notifications...');
    const oldNotificationsCount = await db('notifications')
      .where('is_read', true)
      .where('created_at', '<', db.raw("CURRENT_TIMESTAMP - INTERVAL '30 days'"))
      .del();
    
    logger.info(`🔔 Deleted ${oldNotificationsCount} old read notifications`);

    // Update statistics
    logger.info('📊 Updating database statistics...');
    await db.raw('ANALYZE');

    // Vacuum database (PostgreSQL specific)
    if (config.NODE_ENV !== 'production') {
      logger.info('🗜️ Vacuuming database...');
      await db.raw('VACUUM');
    }

    logger.info('✅ Database cleanup completed successfully!');

    // Display cleanup summary
    const totalCleaned = expiredOtpCount + expiredSessionsCount + oldAuditLogsCount + 
                        orphanedFiles.length + oldNotificationsCount;
    
    logger.info('\n📊 Cleanup Summary:');
    logger.info(`  🔑 Expired OTP codes: ${expiredOtpCount}`);
    logger.info(`  🔐 Expired sessions: ${expiredSessionsCount}`);
    logger.info(`  📋 Old audit logs: ${oldAuditLogsCount}`);
    logger.info(`  📁 Orphaned files: ${orphanedFiles.length}`);
    logger.info(`  🔔 Old notifications: ${oldNotificationsCount}`);
    logger.info(`  📊 Total records cleaned: ${totalCleaned}`);

  } catch (error) {
    logger.error('❌ Database cleanup failed:', error);
    process.exit(1);
  } finally {
    await db.destroy();
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Database Cleanup Script

Usage: npm run cleanup [options]

This script cleans up:
- Expired OTP codes
- Expired sessions  
- Old audit logs (90+ days)
- Orphaned file uploads (7+ days)
- Old read notifications (30+ days)

Options:
  --help     Show this help message

Examples:
  npm run cleanup          # Run cleanup
  `);
  process.exit(0);
}

cleanupDatabase();

// Handle process termination
process.on('SIGINT', async () => {
  logger.info('🛑 Received SIGINT, closing database connection...');
  await db.destroy();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('🛑 Received SIGTERM, closing database connection...');
  await db.destroy();
  process.exit(0);
});
