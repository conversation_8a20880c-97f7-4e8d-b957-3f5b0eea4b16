import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, UserRole } from '@/types'
import { authService } from '../services/authService'

interface AuthState {
  user: User | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (credentials: { phone_number: string; password?: string; otp?: string }) => Promise<void>
  logout: () => Promise<void>
  refreshAccessToken: () => Promise<void>
  updateUser: (user: Partial<User>) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  checkAuth: () => Promise<void>
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,

      // Actions
      login: async (credentials) => {
        try {
          set({ isLoading: true, error: null })
          
          const response = await authService.login(credentials)
          
          set({
            user: response.user,
            accessToken: response.access_token,
            refreshToken: response.refresh_token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message || 'فشل في تسجيل الدخول'
          })
          throw error
        }
      },

      logout: async () => {
        try {
          const { refreshToken } = get()
          if (refreshToken) {
            await authService.logout(refreshToken)
          }
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      },

      refreshAccessToken: async () => {
        try {
          const { refreshToken } = get()
          if (!refreshToken) {
            throw new Error('No refresh token available')
          }

          const response = await authService.refreshToken(refreshToken)
          
          set({
            accessToken: response.access_token,
            refreshToken: response.refresh_token,
            error: null
          })
        } catch (error: any) {
          console.error('Token refresh failed:', error)
          // If refresh fails, logout user
          get().logout()
          throw error
        }
      },

      updateUser: (userData) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData }
          })
        }
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      checkAuth: async () => {
        try {
          const { accessToken, refreshToken } = get()
          
          if (!accessToken || !refreshToken) {
            set({ isLoading: false, isAuthenticated: false })
            return
          }

          // Try to get user profile to verify token
          try {
            const user = await authService.getProfile()
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null
            })
          } catch (error: any) {
            // If profile request fails, try to refresh token
            if (error.status === 401) {
              try {
                await get().refreshAccessToken()
                const user = await authService.getProfile()
                set({
                  user,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null
                })
              } catch (refreshError) {
                // If refresh also fails, logout
                await get().logout()
              }
            } else {
              throw error
            }
          }
        } catch (error: any) {
          console.error('Auth check failed:', error)
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// Helper functions
export const useUser = () => useAuthStore(state => state.user)
export const useIsAuthenticated = () => useAuthStore(state => state.isAuthenticated)
export const useAccessToken = () => useAuthStore(state => state.accessToken)

// Role-based access helpers
export const useHasRole = (role: UserRole) => {
  const user = useUser()
  return user?.role === role
}

export const useHasAnyRole = (roles: UserRole[]) => {
  const user = useUser()
  return user ? roles.includes(user.role) : false
}

export const useIsAdmin = () => useHasRole(UserRole.ADMIN)
export const useIsClient = () => useHasRole(UserRole.CLIENT)

export const useIsTechnician = () => useHasAnyRole([
  UserRole.MEASUREMENT,
  UserRole.DESIGNER,
  UserRole.CUTTING,
  UserRole.PRESSING,
  UserRole.VACUUM,
  UserRole.EDGE_CUTTING,
  UserRole.TAPE_APPLICATION,
  UserRole.PACKAGING,
  UserRole.DELIVERY
])

// Initialize auth check on app start
if (typeof window !== 'undefined') {
  useAuthStore.getState().checkAuth()
}
