import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, UserRole } from '../types'
import { authService } from '../services/authService'

interface AuthState {
  user: User | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (credentials: { phone_number: string; password?: string; otp?: string }) => Promise<void>
  logout: () => Promise<void>
  refreshAccessToken: () => Promise<void>
  updateUser: (user: Partial<User>) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  checkAuth: () => Promise<void>
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,

      // Actions
      login: async (credentials) => {
        try {
          set({ isLoading: true, error: null })

          // تسجيل دخول مبسط للاختبار
          if (credentials.phone_number === 'admin' && credentials.password === '123') {
            const mockUser: User = {
              id: '1',
              name: 'المدير العام',
              phone_number: 'admin',
              role: UserRole.ADMIN,
              is_active: true,
              created_at: new Date(),
              updated_at: new Date()
            }

            set({
              user: mockUser,
              accessToken: 'mock-access-token',
              refreshToken: 'mock-refresh-token',
              isAuthenticated: true,
              isLoading: false,
              error: null
            })
          } else {
            throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة')
          }
        } catch (error: any) {
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message || 'فشل في تسجيل الدخول'
          })
          throw error
        }
      },

      logout: async () => {
        try {
          const { refreshToken } = get()
          if (refreshToken) {
            await authService.logout(refreshToken)
          }
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      },

      refreshAccessToken: async () => {
        try {
          const { refreshToken } = get()
          if (!refreshToken) {
            throw new Error('No refresh token available')
          }

          const response = await authService.refreshToken(refreshToken)
          
          set({
            accessToken: response.access_token,
            refreshToken: response.refresh_token,
            error: null
          })
        } catch (error: any) {
          console.error('Token refresh failed:', error)
          // If refresh fails, logout user
          get().logout()
          throw error
        }
      },

      updateUser: (userData) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData }
          })
        }
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      checkAuth: async () => {
        try {
          const { accessToken, user } = get()

          if (!accessToken || !user) {
            set({ isLoading: false, isAuthenticated: false })
            return
          }

          // للاختبار: إذا كان هناك token وuser، اعتبر المستخدم مسجل دخول
          set({
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          console.error('Auth check failed:', error)
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// Helper functions
export const useUser = () => useAuthStore(state => state.user)
export const useIsAuthenticated = () => useAuthStore(state => state.isAuthenticated)
export const useAccessToken = () => useAuthStore(state => state.accessToken)

// Role-based access helpers
export const useHasRole = (role: UserRole) => {
  const user = useUser()
  return user?.role === role
}

export const useHasAnyRole = (roles: UserRole[]) => {
  const user = useUser()
  return user ? roles.includes(user.role) : false
}

export const useIsAdmin = () => useHasRole(UserRole.ADMIN)
export const useIsClient = () => useHasRole(UserRole.CLIENT)

export const useIsTechnician = () => useHasAnyRole([
  UserRole.MEASUREMENT,
  UserRole.DESIGNER,
  UserRole.CUTTING,
  UserRole.PRESSING,
  UserRole.VACUUM,
  UserRole.EDGE_CUTTING,
  UserRole.TAPE_APPLICATION,
  UserRole.PACKAGING,
  UserRole.DELIVERY
])

// Initialize auth check on app start
if (typeof window !== 'undefined') {
  useAuthStore.getState().checkAuth()
}
