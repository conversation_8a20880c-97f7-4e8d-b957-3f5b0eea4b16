﻿import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, Typography, Form, Input, Button, Row, Col, Select, InputNumber, Space, message, Modal } from 'antd'
import { ArrowLeftOutlined, UserOutlined, PhoneOutlined, EnvironmentOutlined, SaveOutlined } from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const CreateOrderPage: React.FC = () => {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [gpsLocation, setGpsLocation] = useState<{lat: number, lng: number} | null>(null)
  const [locationLoading, setLocationLoading] = useState(false)

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} د.ع`
  }

  const getCurrentLocation = () => {
    setLocationLoading(true)
    
    if (!navigator.geolocation) {
      message.error('المتصفح لا يدعم خدمة تحديد الموقع')
      setLocationLoading(false)
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords
        setGpsLocation({ lat: latitude, lng: longitude })
        form.setFieldValue('location', `البصرة - الموقع الحالي (${latitude.toFixed(6)}, ${longitude.toFixed(6)})`)
        message.success('تم تحديد الموقع بنجاح')
        setLocationLoading(false)
      },
      (error) => {
        console.error('GPS Error:', error)
        message.error('فشل في تحديد الموقع. يرجى إدخال الموقع يدوياً')
        setLocationLoading(false)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    )
  }

  const calculatePrice = (width: number, height: number, pattern: string) => {
    const area = (width * height) / 10000
    const basePrice = pattern === 'سادة' ? 120000 :
                     pattern === 'عروق خشب' ? 150000 :
                     pattern === 'خطوط عصرية' ? 140000 :
                     pattern === 'لوحة كلاسيكية' ? 160000 :
                     pattern === 'هندسي' ? 145000 : 120000
    
    return Math.round(area * basePrice)
  }

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)
      const price = calculatePrice(values.door_width, values.door_height, values.door_pattern)

      const newOrder = {
        id: Date.now(),
        order_id: `ORD-${Date.now()}`,
        client_name: values.client_name,
        phone_number: values.phone_number,
        location: values.location,
        notes: values.notes || '',
        door_width: values.door_width,
        door_height: values.door_height,
        door_color: values.door_color,
        door_pattern: values.door_pattern,
        door_opening: values.door_opening,
        status: 'new',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        total: price,
        gps_coordinates: gpsLocation,
        items: [{
          width: values.door_width,
          height: values.door_height,
          color: values.door_color,
          pattern: values.door_pattern,
          opening_type: values.door_opening,
          price: price
        }]
      }

      console.log('Order Data:', newOrder)

      // حفظ الطلب في localStorage
      const existingOrders = JSON.parse(localStorage.getItem('orders') || '[]')
      existingOrders.unshift(newOrder) // إضافة في المقدمة
      localStorage.setItem('orders', JSON.stringify(existingOrders))

      await new Promise(resolve => setTimeout(resolve, 1500))

      message.success({
        content: `تم إنشاء الطلب بنجاح! رقم الطلب: ${newOrder.order_id}`,
        duration: 5
      })

      setHasUnsavedChanges(false)
      navigate('/orders')

    } catch (error) {
      message.error('حدث خطأ أثناء إنشاء الطلب')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  return (
    <>
      <Helmet>
        <title>طلب جديد - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <Space style={{ marginBottom: 16 }}>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => {
                if (hasUnsavedChanges) {
                  Modal.confirm({
                    title: 'تحذير',
                    content: 'لديك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟',
                    okText: 'نعم، غادر',
                    cancelText: 'إلغاء',
                    onOk: () => navigate('/orders')
                  })
                } else {
                  navigate('/orders')
                }
              }}
            >
              العودة للطلبات
            </Button>
          </Space>
          
          <Title level={2}>إنشاء طلب جديد</Title>
          <Text type="secondary">
            نموذج مبسط لإنشاء طلب باب واحد مع GPS للموقع في البصرة
          </Text>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onValuesChange={() => setHasUnsavedChanges(true)}
          initialValues={{
            door_width: 80,
            door_height: 200,
            door_color: 'بني داكن',
            door_pattern: 'عروق خشب',
            door_opening: 'بدون فتحة'
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="معلومات العميل" size="small">
                <Row gutter={16}>
                  <Col xs={24}>
                    <Form.Item
                      name="client_name"
                      label="اسم العميل"
                      rules={[{ required: true, message: 'يرجى إدخال اسم العميل' }]}
                    >
                      <Input 
                        prefix={<UserOutlined />}
                        placeholder="أدخل اسم العميل"
                        size="large"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24}>
                    <Form.Item
                      name="phone_number"
                      label="رقم الهاتف"
                      rules={[
                        { required: true, message: 'يرجى إدخال رقم الهاتف' },
                        { pattern: /^07[0-9]{9}$/, message: 'رقم الهاتف غير صحيح (مثال: 07901234567)' }
                      ]}
                    >
                      <Input 
                        prefix={<PhoneOutlined />}
                        placeholder="07901234567"
                        dir="ltr"
                        size="large"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24}>
                    <Form.Item
                      name="location"
                      label="الموقع في البصرة"
                      rules={[{ required: true, message: 'يرجى إدخال الموقع أو استخدام GPS' }]}
                    >
                      <Input.Group compact>
                        <Input 
                          prefix={<EnvironmentOutlined />}
                          placeholder="البصرة، العشار"
                          style={{ width: 'calc(100% - 120px)' }}
                          size="large"
                        />
                        <Button 
                          type="primary"
                          loading={locationLoading}
                          onClick={getCurrentLocation}
                          style={{ width: 120 }}
                          size="large"
                        >
                          📍 GPS
                        </Button>
                      </Input.Group>
                    </Form.Item>
                    {gpsLocation && (
                      <div style={{ 
                        fontSize: 12, 
                        color: '#52c41a', 
                        marginTop: 4,
                        padding: 8,
                        backgroundColor: '#f6ffed',
                        borderRadius: 4,
                        border: '1px solid #b7eb8f'
                      }}>
                        ✅ تم تحديد الموقع: {gpsLocation.lat.toFixed(6)}, {gpsLocation.lng.toFixed(6)}
                      </div>
                    )}
                  </Col>
                  <Col xs={24}>
                    <Form.Item
                      name="notes"
                      label="ملاحظات إضافية"
                    >
                      <TextArea 
                        rows={3}
                        placeholder="أي ملاحظات خاصة بالطلب..."
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="تفاصيل الباب" size="small">
                <Row gutter={16}>
                  <Col xs={12}>
                    <Form.Item
                      name="door_width"
                      label="العرض (سم)"
                      rules={[{ required: true, message: 'مطلوب' }]}
                    >
                      <InputNumber
                        min={60}
                        max={120}
                        placeholder="80"
                        style={{ width: '100%' }}
                        size="large"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={12}>
                    <Form.Item
                      name="door_height"
                      label="الارتفاع (سم)"
                      rules={[{ required: true, message: 'مطلوب' }]}
                    >
                      <InputNumber
                        min={180}
                        max={250}
                        placeholder="200"
                        style={{ width: '100%' }}
                        size="large"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24}>
                    <Form.Item
                      name="door_color"
                      label="اللون"
                      rules={[{ required: true, message: 'مطلوب' }]}
                    >
                      <Select 
                        placeholder="اختر اللون"
                        size="large"
                      >
                        <Option value="أبيض">أبيض</Option>
                        <Option value="بني فاتح">بني فاتح</Option>
                        <Option value="بني داكن">بني داكن</Option>
                        <Option value="أسود">أسود</Option>
                        <Option value="رمادي">رمادي</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={24}>
                    <Form.Item
                      name="door_pattern"
                      label="النقش"
                      rules={[{ required: true, message: 'مطلوب' }]}
                    >
                      <Select 
                        placeholder="اختر النقش"
                        size="large"
                      >
                        <Option value="سادة">سادة - 120,000 د.ع</Option>
                        <Option value="عروق خشب">عروق خشب - 150,000 د.ع</Option>
                        <Option value="خطوط عصرية">خطوط عصرية - 140,000 د.ع</Option>
                        <Option value="لوحة كلاسيكية">لوحة كلاسيكية - 160,000 د.ع</Option>
                        <Option value="هندسي">هندسي - 145,000 د.ع</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={24}>
                    <Form.Item
                      name="door_opening"
                      label="نوع الفتحة"
                      rules={[{ required: true, message: 'مطلوب' }]}
                    >
                      <Select 
                        placeholder="اختر نوع الفتحة"
                        size="large"
                      >
                        <Option value="بدون فتحة">بدون فتحة</Option>
                        <Option value="فتحة زجاج">فتحة زجاج</Option>
                        <Option value="تهوية">تهوية</Option>
                        <Option value="فتحة زجاج مع تهوية">فتحة زجاج مع تهوية</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item noStyle shouldUpdate>
                  {() => {
                    const values = form.getFieldsValue()
                    const price = calculatePrice(values.door_width || 80, values.door_height || 200, values.door_pattern || 'عروق خشب')
                    
                    return (
                      <div style={{ 
                        marginTop: 16, 
                        padding: 16, 
                        backgroundColor: '#f9f9f9', 
                        borderRadius: 8,
                        textAlign: 'center'
                      }}>
                        <Text strong style={{ display: 'block', marginBottom: 12 }}>
                          معاينة الباب
                        </Text>
                        <div style={{ 
                          width: 100,
                          height: 150,
                          border: '3px solid #333',
                          borderRadius: 6,
                          margin: '0 auto',
                          backgroundColor: values.door_color === 'أبيض' ? '#ffffff' : 
                                         values.door_color === 'بني فاتح' ? '#D2B48C' :
                                         values.door_color === 'بني داكن' ? '#8B4513' :
                                         values.door_color === 'أسود' ? '#000000' : '#808080',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          position: 'relative'
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            right: 12,
                            width: 16,
                            height: 16,
                            backgroundColor: '#FFD700',
                            borderRadius: '50%',
                            border: '2px solid #333'
                          }} />
                          <Text style={{ 
                            color: values.door_color === 'أبيض' ? '#333' : '#fff',
                            fontSize: 10,
                            textAlign: 'center'
                          }}>
                            {values.door_pattern}
                          </Text>
                        </div>
                        <div style={{ marginTop: 8, fontSize: 12 }}>
                          {values.door_width || 80} × {values.door_height || 200} سم
                        </div>
                        <div style={{ marginTop: 4, fontSize: 16, fontWeight: 'bold', color: '#1890ff' }}>
                          {formatCurrency(price)}
                        </div>
                      </div>
                    )
                  }}
                </Form.Item>
              </Card>
            </Col>
          </Row>

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
              style={{ minWidth: 200, height: 50 }}
            >
              إنشاء الطلب
            </Button>
          </div>
        </Form>
      </div>
    </>
  )
}

export default CreateOrderPage
