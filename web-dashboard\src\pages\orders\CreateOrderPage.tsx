import React from 'react'
import { Card, Typography, Result } from 'antd'
import { Helmet } from 'react-helmet-async'

const { Title } = Typography

const CreateOrderPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>طلب جديد - معمل الشمس</title>
      </Helmet>

      <div>
        <Title level={2}>إنشاء طلب جديد</Title>
        
        <Card>
          <Result
            status="info"
            title="صفحة قيد التطوير"
            subTitle="نموذج إنشاء الطلبات الجديدة سيكون متاحاً قريباً"
          />
        </Card>
      </div>
    </>
  )
}

export default CreateOrderPage
