import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Card,
  Typography,
  Form,
  Input,
  Button,
  Row,
  Col,
  Select,
  InputNumber,
  Space,
  Divider,
  message,
  Steps,
  Modal,
  Image,
  Tooltip
} from 'antd'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  SaveOutlined,
  CheckCircleOutlined
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

interface OrderItem {
  width: number
  height: number
  color_id: string
  pattern_id: string
  opening_type: string
  notes?: string
}

interface OrderFormData {
  client_name: string
  phone_number: string
  email?: string
  location: string
  notes?: string
  items: OrderItem[]
}

const CreateOrderPage: React.FC = () => {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // تحذير عند مغادرة الصفحة مع بيانات غير محفوظة
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // تتبع التغييرات في النموذج
  const handleFormChange = () => {
    setHasUnsavedChanges(true)
  }

  // دالة لتنسيق العملة العراقية
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} د.ع`
  }

  // بيانات تجريبية للألوان والنقوش
  const colors = [
    { id: '1', name: 'أبيض', name_ar: 'أبيض', price_multiplier: 1.0 },
    { id: '2', name: 'بني فاتح', name_ar: 'بني فاتح', price_multiplier: 1.1 },
    { id: '3', name: 'بني داكن', name_ar: 'بني داكن', price_multiplier: 1.2 },
    { id: '4', name: 'أسود', name_ar: 'أسود', price_multiplier: 1.25 },
    { id: '5', name: 'رمادي', name_ar: 'رمادي', price_multiplier: 1.1 }
  ]

  const patterns = [
    {
      id: '1',
      name: 'سادة',
      name_ar: 'سادة',
      base_price: 120000,
      image_url: '/images/pattern-plain.svg',
      description: 'تصميم بسيط وأنيق بدون نقوش'
    },
    {
      id: '2',
      name: 'عروق خشب',
      name_ar: 'عروق خشب',
      base_price: 150000,
      image_url: '/images/pattern-wood.svg',
      description: 'نقش يحاكي عروق الخشب الطبيعي'
    },
    {
      id: '3',
      name: 'خطوط عصرية',
      name_ar: 'خطوط عصرية',
      base_price: 140000,
      image_url: '/images/pattern-modern.svg',
      description: 'خطوط هندسية عصرية ومتوازية'
    },
    {
      id: '4',
      name: 'لوحة كلاسيكية',
      name_ar: 'لوحة كلاسيكية',
      base_price: 160000,
      image_url: '/images/pattern-classic.svg',
      description: 'تصميم كلاسيكي مع إطارات مرفوعة'
    },
    {
      id: '5',
      name: 'هندسي',
      name_ar: 'هندسي',
      base_price: 145000,
      image_url: '/images/pattern-geometric.svg',
      description: 'أشكال هندسية معاصرة ومتداخلة'
    }
  ]

  const openingTypes = [
    'بدون فتحة',
    'فتحة زجاج',
    'تهوية',
    'فتحة زجاج مع تهوية'
  ]

  // حساب سعر الباب
  const calculateDoorPrice = (item: Partial<OrderItem>) => {
    if (!item.width || !item.height || !item.color_id || !item.pattern_id) {
      return 0
    }

    const color = colors.find(c => c.id === item.color_id)
    const pattern = patterns.find(p => p.id === item.pattern_id)

    if (!color || !pattern) return 0

    const area = (item.width * item.height) / 10000 // تحويل إلى متر مربع
    const basePrice = pattern.base_price
    const finalPrice = basePrice * area * color.price_multiplier

    return Math.round(finalPrice)
  }

  // حساب المجموع الكلي
  const calculateTotal = (items: Partial<OrderItem>[]) => {
    return items.reduce((total, item) => total + calculateDoorPrice(item), 0)
  }

  const handleSubmit = async (values: OrderFormData) => {
    try {
      setLoading(true)

      // التحقق من صحة البيانات
      if (!values.items || values.items.length === 0) {
        message.error('يرجى إضافة باب واحد على الأقل')
        return
      }

      // حساب الأسعار للعناصر
      const itemsWithPrices = values.items.map(item => ({
        ...item,
        price: calculateDoorPrice(item)
      }))

      const total = calculateTotal(values.items)

      if (total === 0) {
        message.error('لا يمكن إنشاء طلب بمبلغ صفر')
        return
      }

      const orderData = {
        ...values,
        items: itemsWithPrices,
        total,
        status: 'new',
        created_at: new Date().toISOString(),
        order_id: `ORD-${Date.now()}`
      }

      console.log('Order Data:', orderData)

      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 1500))

      // هنا سيتم إرسال البيانات للـ API
      message.success({
        content: `تم إنشاء الطلب بنجاح! رقم الطلب: ${orderData.order_id}`,
        duration: 5
      })

      setHasUnsavedChanges(false) // إزالة تحذير المغادرة
      navigate('/orders')

    } catch (error) {
      message.error('حدث خطأ أثناء إنشاء الطلب')
    } finally {
      setLoading(false)
    }
  }

  const steps = [
    {
      title: 'معلومات العميل',
      content: 'client-info'
    },
    {
      title: 'تفاصيل الأبواب',
      content: 'door-details'
    },
    {
      title: 'مراجعة وإرسال',
      content: 'review'
    }
  ]

  return (
    <>
      <Helmet>
        <title>طلب جديد - معمل الشمس</title>
      </Helmet>

      <div>
        {/* Header */}
        <div style={{ marginBottom: 24 }}>
          <Space style={{ marginBottom: 16 }}>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                if (hasUnsavedChanges) {
                  Modal.confirm({
                    title: 'تحذير',
                    content: 'لديك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟',
                    okText: 'نعم، غادر',
                    cancelText: 'إلغاء',
                    onOk: () => navigate('/orders')
                  })
                } else {
                  navigate('/orders')
                }
              }}
            >
              العودة للطلبات
            </Button>
          </Space>

          <Title level={2}>إنشاء طلب جديد</Title>
          <Text type="secondary">
            أدخل تفاصيل الطلب الجديد
          </Text>
        </div>

        {/* Steps */}
        <Card style={{ marginBottom: 24 }}>
          <Steps current={currentStep} items={steps} />
        </Card>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onValuesChange={handleFormChange}
          initialValues={{
            items: [{}] // باب واحد افتراضي
          }}
        >
          {/* معلومات العميل */}
          {currentStep === 0 && (
            <Card title="معلومات العميل">
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="client_name"
                    label="اسم العميل"
                    rules={[{ required: true, message: 'يرجى إدخال اسم العميل' }]}
                  >
                    <Input
                      prefix={<UserOutlined />}
                      placeholder="أدخل اسم العميل"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="phone_number"
                    label="رقم الهاتف"
                    rules={[
                      { required: true, message: 'يرجى إدخال رقم الهاتف' },
                      { pattern: /^07[0-9]{9}$/, message: 'رقم الهاتف غير صحيح (مثال: 07901234567)' }
                    ]}
                  >
                    <Input
                      prefix={<PhoneOutlined />}
                      placeholder="07901234567"
                      dir="ltr"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="email"
                    label="البريد الإلكتروني (اختياري)"
                    rules={[{ type: 'email', message: 'البريد الإلكتروني غير صحيح' }]}
                  >
                    <Input
                      placeholder="<EMAIL>"
                      dir="ltr"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="location"
                    label="الموقع"
                    rules={[{ required: true, message: 'يرجى إدخال الموقع' }]}
                  >
                    <Input
                      prefix={<EnvironmentOutlined />}
                      placeholder="بغداد، الكرادة"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    name="notes"
                    label="ملاحظات إضافية"
                  >
                    <TextArea
                      rows={3}
                      placeholder="أي ملاحظات خاصة بالطلب..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <div style={{ textAlign: 'left', marginTop: 24 }}>
                <Button
                  type="primary"
                  onClick={() => {
                    form.validateFields(['client_name', 'phone_number', 'location'])
                      .then((values) => {
                        // حفظ البيانات في النموذج
                        console.log('Step 1 values:', values)
                        setCurrentStep(1)
                      })
                      .catch(() => message.error('يرجى إكمال جميع الحقول المطلوبة'))
                  }}
                >
                  التالي
                </Button>
              </div>
            </Card>
          )}

          {/* تفاصيل الأبواب */}
          {currentStep === 1 && (
            <Card title="تفاصيل الأبواب">
              <Form.List name="items">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }, index) => (
                      <Card
                        key={key}
                        type="inner"
                        title={`الباب ${index + 1}`}
                        extra={
                          fields.length > 1 && (
                            <Button
                              type="text"
                              danger
                              icon={<DeleteOutlined />}
                              onClick={() => remove(name)}
                            >
                              حذف
                            </Button>
                          )
                        }
                        style={{ marginBottom: 16 }}
                      >
                        <Row gutter={16}>
                          <Col xs={24} sm={8}>
                            <Form.Item
                              {...restField}
                              name={[name, 'width']}
                              label="العرض (سم)"
                              rules={[{ required: true, message: 'مطلوب' }]}
                            >
                              <InputNumber
                                min={60}
                                max={120}
                                placeholder="80"
                                style={{ width: '100%' }}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={8}>
                            <Form.Item
                              {...restField}
                              name={[name, 'height']}
                              label="الارتفاع (سم)"
                              rules={[{ required: true, message: 'مطلوب' }]}
                            >
                              <InputNumber
                                min={180}
                                max={250}
                                placeholder="200"
                                style={{ width: '100%' }}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={8}>
                            <Form.Item
                              {...restField}
                              name={[name, 'color_id']}
                              label="اللون"
                              rules={[{ required: true, message: 'مطلوب' }]}
                            >
                              <Select placeholder="اختر اللون">
                                {colors.map(color => (
                                  <Option key={color.id} value={color.id}>
                                    {color.name_ar}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={8}>
                            <Form.Item
                              {...restField}
                              name={[name, 'pattern_id']}
                              label="النقش"
                              rules={[{ required: true, message: 'مطلوب' }]}
                            >
                              <Select
                                placeholder="اختر النقش"
                                optionLabelProp="label"
                              >
                                {patterns.map(pattern => (
                                  <Option
                                    key={pattern.id}
                                    value={pattern.id}
                                    label={pattern.name_ar}
                                  >
                                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                      <Image
                                        src={pattern.image_url}
                                        alt={pattern.name_ar}
                                        width={40}
                                        height={30}
                                        style={{ borderRadius: 4, objectFit: 'cover' }}
                                        preview={false}
                                      />
                                      <div>
                                        <div style={{ fontWeight: 'bold' }}>{pattern.name_ar}</div>
                                        <div style={{ fontSize: 12, color: '#666' }}>
                                          {formatCurrency(pattern.base_price)}
                                        </div>
                                      </div>
                                    </div>
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={8}>
                            <Form.Item
                              {...restField}
                              name={[name, 'opening_type']}
                              label="نوع الفتحة"
                              rules={[{ required: true, message: 'مطلوب' }]}
                            >
                              <Select placeholder="اختر نوع الفتحة">
                                {openingTypes.map(type => (
                                  <Option key={type} value={type}>
                                    {type}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={8}>
                            <Form.Item label="السعر المتوقع">
                              <Form.Item noStyle shouldUpdate>
                                {() => {
                                  const items = form.getFieldValue('items') || []
                                  const currentItem = items[index] || {}
                                  const price = calculateDoorPrice(currentItem)
                                  return (
                                    <Input
                                      value={price > 0 ? formatCurrency(price) : 'غير محسوب'}
                                      disabled
                                      style={{
                                        color: price > 0 ? '#52c41a' : '#999',
                                        fontWeight: 'bold'
                                      }}
                                    />
                                  )
                                }}
                              </Form.Item>
                            </Form.Item>
                          </Col>
                          <Col xs={24}>
                            <Form.Item
                              {...restField}
                              name={[name, 'notes']}
                              label="ملاحظات خاصة بالباب"
                            >
                              <TextArea
                                rows={2}
                                placeholder="أي ملاحظات خاصة بهذا الباب..."
                              />
                            </Form.Item>
                          </Col>

                          {/* معاينة الباب */}
                          <Col xs={24}>
                            <Form.Item noStyle shouldUpdate>
                              {() => {
                                const items = form.getFieldValue('items') || []
                                const currentItem = items[index] || {}
                                const selectedPattern = patterns.find(p => p.id === currentItem.pattern_id)
                                const selectedColor = colors.find(c => c.id === currentItem.color_id)

                                if (selectedPattern && selectedColor) {
                                  return (
                                    <div style={{
                                      marginTop: 16,
                                      padding: 12,
                                      backgroundColor: '#f9f9f9',
                                      borderRadius: 8,
                                      border: '1px solid #d9d9d9'
                                    }}>
                                      <Text strong style={{ marginBottom: 8, display: 'block' }}>
                                        معاينة الباب:
                                      </Text>
                                      <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                                        <div style={{
                                          position: 'relative',
                                          width: 80,
                                          height: 120,
                                          border: '2px solid #333',
                                          borderRadius: 4,
                                          overflow: 'hidden',
                                          backgroundColor: selectedColor.name === 'أبيض' ? '#ffffff' :
                                                         selectedColor.name === 'بني فاتح' ? '#D2B48C' :
                                                         selectedColor.name === 'بني داكن' ? '#8B4513' :
                                                         selectedColor.name === 'أسود' ? '#000000' : '#808080'
                                        }}>
                                          <Image
                                            src={selectedPattern.image_url}
                                            alt={selectedPattern.name_ar}
                                            width="100%"
                                            height="100%"
                                            style={{
                                              objectFit: 'cover',
                                              opacity: 0.8,
                                              mixBlendMode: 'multiply'
                                            }}
                                            preview={false}
                                          />
                                          <div style={{
                                            position: 'absolute',
                                            top: '50%',
                                            right: 8,
                                            width: 12,
                                            height: 12,
                                            backgroundColor: '#FFD700',
                                            borderRadius: '50%',
                                            border: '1px solid #333'
                                          }} />
                                        </div>
                                        <div>
                                          <div><Text strong>اللون:</Text> {selectedColor.name_ar}</div>
                                          <div><Text strong>النقش:</Text> {selectedPattern.name_ar}</div>
                                          <div><Text strong>الأبعاد:</Text> {currentItem.width || 0} × {currentItem.height || 0} سم</div>
                                          <div><Text strong>السعر:</Text> <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                                            {formatCurrency(calculateDoorPrice(currentItem))}
                                          </Text></div>
                                        </div>
                                      </div>
                                    </div>
                                  )
                                }
                                return null
                              }}
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}

                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusOutlined />}
                      style={{ marginBottom: 16 }}
                    >
                      إضافة باب آخر
                    </Button>
                  </>
                )}
              </Form.List>

              {/* المجموع الكلي */}
              <Card type="inner" style={{ backgroundColor: '#f0f2f5' }}>
                <Form.Item noStyle shouldUpdate>
                  {() => {
                    const items = form.getFieldValue('items') || []
                    const total = calculateTotal(items)
                    return (
                      <div style={{ textAlign: 'center' }}>
                        <Text>المجموع الكلي المتوقع:</Text>
                        <br />
                        <Text
                          strong
                          style={{
                            fontSize: 24,
                            color: '#1890ff'
                          }}
                        >
                          {total > 0 ? formatCurrency(total) : 'غير محسوب'}
                        </Text>
                      </div>
                    )
                  }}
                </Form.Item>
              </Card>

              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 24 }}>
                <Button onClick={() => setCurrentStep(0)}>
                  السابق
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    // التحقق من صحة بيانات الأبواب
                    form.validateFields()
                      .then((values) => {
                        const items = values.items || []
                        const hasValidItems = items.some((item: any) =>
                          item && item.width && item.height && item.color_id && item.pattern_id && item.opening_type
                        )

                        if (!hasValidItems) {
                          message.error('يرجى إضافة باب واحد على الأقل مع جميع التفاصيل')
                          return
                        }

                        console.log('Step 2 values:', values)
                        setCurrentStep(2)
                      })
                      .catch((errorInfo) => {
                        console.log('Validation failed:', errorInfo)
                        message.error('يرجى إكمال جميع الحقول المطلوبة')
                      })
                  }}
                >
                  التالي
                </Button>
              </div>
            </Card>
          )}

          {/* مراجعة وإرسال */}
          {currentStep === 2 && (
            <Card title="مراجعة الطلب">
              <Form.Item noStyle shouldUpdate>
                {() => {
                  const formData = form.getFieldsValue()
                  console.log('Review formData:', formData) // للتشخيص
                  const total = calculateTotal(formData.items || [])

                  return (
                    <div>
                      <Title level={4}>معلومات العميل</Title>
                      <Row gutter={16} style={{ marginBottom: 24 }}>
                        <Col span={12}>
                          <Text strong>الاسم: </Text>
                          <Text>{formData.client_name}</Text>
                        </Col>
                        <Col span={12}>
                          <Text strong>الهاتف: </Text>
                          <Text dir="ltr">{formData.phone_number}</Text>
                        </Col>
                        <Col span={12}>
                          <Text strong>الموقع: </Text>
                          <Text>{formData.location}</Text>
                        </Col>
                        {formData.email && (
                          <Col span={12}>
                            <Text strong>البريد: </Text>
                            <Text dir="ltr">{formData.email}</Text>
                          </Col>
                        )}
                      </Row>

                      <Divider />

                      <Title level={4}>تفاصيل الأبواب ({formData.items?.length || 0})</Title>
                      {formData.items?.map((item: any, index: number) => {
                        const color = colors.find(c => c.id === item.color_id)
                        const pattern = patterns.find(p => p.id === item.pattern_id)
                        const price = calculateDoorPrice(item)
                        const area = item.width && item.height ? ((item.width * item.height) / 10000).toFixed(2) : '0'

                        return (
                          <Card
                            key={index}
                            type="inner"
                            title={`الباب ${index + 1}`}
                            style={{ marginBottom: 16 }}
                            extra={
                              <Text strong style={{ color: '#1890ff', fontSize: 16 }}>
                                {formatCurrency(price)}
                              </Text>
                            }
                          >
                            <Row gutter={16}>
                              {/* معاينة الباب */}
                              <Col xs={24} sm={8}>
                                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                                  <div style={{
                                    position: 'relative',
                                    width: 100,
                                    height: 150,
                                    border: '3px solid #333',
                                    borderRadius: 6,
                                    overflow: 'hidden',
                                    margin: '0 auto',
                                    backgroundColor: color?.name === 'أبيض' ? '#ffffff' :
                                                   color?.name === 'بني فاتح' ? '#D2B48C' :
                                                   color?.name === 'بني داكن' ? '#8B4513' :
                                                   color?.name === 'أسود' ? '#000000' : '#808080'
                                  }}>
                                    {pattern?.image_url && (
                                      <Image
                                        src={pattern.image_url}
                                        alt={pattern.name_ar}
                                        width="100%"
                                        height="100%"
                                        style={{
                                          objectFit: 'cover',
                                          opacity: 0.8,
                                          mixBlendMode: 'multiply'
                                        }}
                                        preview={true}
                                      />
                                    )}
                                    {/* مقبض الباب */}
                                    <div style={{
                                      position: 'absolute',
                                      top: '50%',
                                      right: 12,
                                      width: 16,
                                      height: 16,
                                      backgroundColor: '#FFD700',
                                      borderRadius: '50%',
                                      border: '2px solid #333',
                                      transform: 'translateY(-50%)'
                                    }} />
                                  </div>
                                  <Text type="secondary" style={{ fontSize: 12 }}>
                                    معاينة الباب
                                  </Text>
                                </div>
                              </Col>

                              {/* تفاصيل الباب */}
                              <Col xs={24} sm={16}>
                                <Row gutter={[16, 8]}>
                                  <Col xs={24} sm={12}>
                                    <Text strong>الأبعاد: </Text>
                                    <Text>{item.width} × {item.height} سم</Text>
                                    <br />
                                    <Text type="secondary" style={{ fontSize: 12 }}>
                                      المساحة: {area} م²
                                    </Text>
                                  </Col>
                                  <Col xs={24} sm={12}>
                                    <Text strong>اللون: </Text>
                                    <Text>{color?.name_ar}</Text>
                                    <br />
                                    <Text type="secondary" style={{ fontSize: 12 }}>
                                      مضاعف: ×{color?.price_multiplier}
                                    </Text>
                                  </Col>
                                  <Col xs={24} sm={12}>
                                    <Text strong>النقش: </Text>
                                    <Text>{pattern?.name_ar}</Text>
                                    <br />
                                    <Text type="secondary" style={{ fontSize: 12 }}>
                                      سعر أساسي: {formatCurrency(pattern?.base_price || 0)}
                                    </Text>
                                  </Col>
                                  <Col xs={24} sm={12}>
                                    <Text strong>نوع الفتحة: </Text>
                                    <Text>{item.opening_type}</Text>
                                  </Col>
                                </Row>
                              </Col>
                            </Row>

                            {item.notes && (
                              <div style={{
                                marginTop: 16,
                                padding: 12,
                                backgroundColor: '#f0f8ff',
                                borderRadius: 6,
                                border: '1px solid #d6e4ff'
                              }}>
                                <Text strong style={{ color: '#1890ff' }}>ملاحظات: </Text>
                                <Text>{item.notes}</Text>
                              </div>
                            )}
                          </Card>
                        )
                      })}

                      <Card
                        type="inner"
                        style={{
                          backgroundColor: '#e6f7ff',
                          textAlign: 'center',
                          marginTop: 16
                        }}
                      >
                        <Text strong style={{ fontSize: 18 }}>
                          المجموع الكلي:
                        </Text>
                        <Text
                          strong
                          style={{
                            fontSize: 24,
                            color: '#1890ff',
                            marginRight: 8
                          }}
                        >
                          {formatCurrency(total)}
                        </Text>
                      </Card>
                    </div>
                  )
                }}
              </Form.Item>

              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 24 }}>
                <Button onClick={() => setCurrentStep(1)}>
                  السابق
                </Button>
                <Button
                  type="primary"
                  loading={loading}
                  icon={<CheckCircleOutlined />}
                  size="large"
                  onClick={() => {
                    const formData = form.getFieldsValue()
                    const total = calculateTotal(formData.items || [])

                    Modal.confirm({
                      title: 'تأكيد إنشاء الطلب',
                      content: (
                        <div>
                          <p><strong>العميل:</strong> {formData.client_name}</p>
                          <p><strong>عدد الأبواب:</strong> {formData.items?.length || 0}</p>
                          <p><strong>المبلغ الإجمالي:</strong> {formatCurrency(total)}</p>
                          <br />
                          <p>هل أنت متأكد من إنشاء هذا الطلب؟</p>
                        </div>
                      ),
                      okText: 'نعم، إنشاء الطلب',
                      cancelText: 'إلغاء',
                      onOk: () => form.submit(),
                      okButtonProps: {
                        icon: <SaveOutlined />,
                        size: 'large'
                      }
                    })
                  }}
                >
                  إنشاء الطلب
                </Button>
              </div>
            </Card>
          )}
        </Form>
      </div>
    </>
  )
}

export default CreateOrderPage
