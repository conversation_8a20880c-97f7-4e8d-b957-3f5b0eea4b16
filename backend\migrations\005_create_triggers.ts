import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create function to update updated_at timestamp
  await knex.raw(`
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';
  `);

  // Create triggers for all tables with updated_at column
  const tables = [
    'users',
    'sessions',
    'otp_codes',
    'colors',
    'patterns',
    'prices_config',
    'orders',
    'order_items',
    'order_stages_log',
    'file_uploads',
    'notifications',
    'system_settings',
    'audit_logs'
  ];

  for (const table of tables) {
    await knex.raw(`
      CREATE TRIGGER update_${table}_updated_at
      BEFORE UPDATE ON ${table}
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    `);
  }

  // Create function to automatically calculate order total price
  await knex.raw(`
    CREATE OR REPLACE FUNCTION update_order_total_price()
    RETURNS TRIGGER AS $$
    BEGIN
        UPDATE orders 
        SET total_price = (
            SELECT COALESCE(SUM(price), 0) 
            FROM order_items 
            WHERE order_id = COALESCE(NEW.order_id, OLD.order_id)
        )
        WHERE id = COALESCE(NEW.order_id, OLD.order_id);
        
        RETURN COALESCE(NEW, OLD);
    END;
    $$ language 'plpgsql';
  `);

  // Create trigger to update order total when order items change
  await knex.raw(`
    CREATE TRIGGER update_order_total_on_item_change
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION update_order_total_price();
  `);

  // Create function to log order status changes
  await knex.raw(`
    CREATE OR REPLACE FUNCTION log_order_status_change()
    RETURNS TRIGGER AS $$
    BEGIN
        IF OLD.status IS DISTINCT FROM NEW.status THEN
            INSERT INTO audit_logs (
                user_id,
                action,
                table_name,
                record_id,
                old_values,
                new_values,
                created_at
            ) VALUES (
                NULL, -- Will be set by application
                'UPDATE',
                'orders',
                NEW.id,
                jsonb_build_object('status', OLD.status),
                jsonb_build_object('status', NEW.status),
                CURRENT_TIMESTAMP
            );
        END IF;
        
        RETURN NEW;
    END;
    $$ language 'plpgsql';
  `);

  // Create trigger for order status changes
  await knex.raw(`
    CREATE TRIGGER log_order_status_change_trigger
    AFTER UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION log_order_status_change();
  `);

  // Create function to clean up expired OTP codes
  await knex.raw(`
    CREATE OR REPLACE FUNCTION cleanup_expired_otp()
    RETURNS void AS $$
    BEGIN
        DELETE FROM otp_codes 
        WHERE expires_at < CURRENT_TIMESTAMP 
        OR (is_used = true AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 hour');
    END;
    $$ language 'plpgsql';
  `);

  // Create function to clean up expired sessions
  await knex.raw(`
    CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
    RETURNS void AS $$
    BEGIN
        DELETE FROM sessions 
        WHERE expires_at < CURRENT_TIMESTAMP;
    END;
    $$ language 'plpgsql';
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers
  const tables = [
    'users',
    'sessions', 
    'otp_codes',
    'colors',
    'patterns',
    'prices_config',
    'orders',
    'order_items',
    'order_stages_log',
    'file_uploads',
    'notifications',
    'system_settings',
    'audit_logs'
  ];

  for (const table of tables) {
    await knex.raw(`DROP TRIGGER IF EXISTS update_${table}_updated_at ON ${table};`);
  }

  await knex.raw(`DROP TRIGGER IF EXISTS update_order_total_on_item_change ON order_items;`);
  await knex.raw(`DROP TRIGGER IF EXISTS log_order_status_change_trigger ON orders;`);

  // Drop functions
  await knex.raw(`DROP FUNCTION IF EXISTS update_updated_at_column();`);
  await knex.raw(`DROP FUNCTION IF EXISTS update_order_total_price();`);
  await knex.raw(`DROP FUNCTION IF EXISTS log_order_status_change();`);
  await knex.raw(`DROP FUNCTION IF EXISTS cleanup_expired_otp();`);
  await knex.raw(`DROP FUNCTION IF EXISTS cleanup_expired_sessions();`);
}
