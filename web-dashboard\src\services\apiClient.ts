import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { message } from 'antd'

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
  pagination?: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

// API Error interface
export interface ApiError {
  message: string
  status: number
  errors?: string[]
}

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const authData = localStorage.getItem('auth-storage')
    if (authData) {
      try {
        const { state } = JSON.parse(authData)
        const token = state?.accessToken
        
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (error) {
        console.error('Error parsing auth data:', error)
      }
    }

    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle responses and errors
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // Log response time in development
    if (import.meta.env.DEV && response.config.metadata) {
      const endTime = new Date()
      const duration = endTime.getTime() - response.config.metadata.startTime.getTime()
      console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`)
    }

    // Return the data directly if it's a successful API response
    if (response.data && typeof response.data === 'object' && 'success' in response.data) {
      if (response.data.success) {
        return {
          ...response,
          data: response.data.data || response.data
        }
      } else {
        // Handle API errors (success: false)
        const error: ApiError = {
          message: response.data.message || 'حدث خطأ غير متوقع',
          status: response.status,
          errors: response.data.errors
        }
        return Promise.reject(error)
      }
    }

    return response
  },
  async (error: AxiosError<ApiResponse>) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean }

    // Handle network errors
    if (!error.response) {
      const networkError: ApiError = {
        message: 'خطأ في الاتصال بالخادم',
        status: 0,
        errors: ['تحقق من اتصالك بالإنترنت']
      }
      message.error(networkError.message)
      return Promise.reject(networkError)
    }

    const { status, data } = error.response

    // Handle 401 Unauthorized - try to refresh token
    if (status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // Get auth store and try to refresh token
        const authData = localStorage.getItem('auth-storage')
        if (authData) {
          const { state } = JSON.parse(authData)
          const refreshToken = state?.refreshToken

          if (refreshToken) {
            // Import auth store dynamically to avoid circular dependency
            const { useAuthStore } = await import('../stores/authStore')
            await useAuthStore.getState().refreshAccessToken()
            
            // Retry original request
            return apiClient(originalRequest)
          }
        }
      } catch (refreshError) {
        // If refresh fails, logout user
        const { useAuthStore } = await import('../stores/authStore')
        useAuthStore.getState().logout()
        
        // Redirect to login
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }

    // Handle other HTTP errors
    const apiError: ApiError = {
      message: data?.message || getErrorMessage(status),
      status,
      errors: data?.errors
    }

    // Show error message for certain status codes
    if (status >= 400 && status < 500 && status !== 401) {
      message.error(apiError.message)
    } else if (status >= 500) {
      message.error('خطأ في الخادم، يرجى المحاولة لاحقاً')
    }

    return Promise.reject(apiError)
  }
)

// Helper function to get error message based on status code
function getErrorMessage(status: number): string {
  switch (status) {
    case 400:
      return 'طلب غير صحيح'
    case 401:
      return 'غير مصرح لك بالوصول'
    case 403:
      return 'ليس لديك صلاحية للوصول'
    case 404:
      return 'المورد المطلوب غير موجود'
    case 409:
      return 'تعارض في البيانات'
    case 422:
      return 'بيانات غير صحيحة'
    case 429:
      return 'تم تجاوز الحد المسموح من الطلبات'
    case 500:
      return 'خطأ في الخادم'
    case 502:
      return 'خطأ في البوابة'
    case 503:
      return 'الخدمة غير متاحة'
    case 504:
      return 'انتهت مهلة الاتصال'
    default:
      return 'حدث خطأ غير متوقع'
  }
}

// Extend AxiosRequestConfig to include metadata
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: {
      startTime: Date
    }
  }
}

export { apiClient }
export default apiClient
