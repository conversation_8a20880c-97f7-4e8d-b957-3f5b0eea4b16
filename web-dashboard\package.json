{"name": "@alshams/web-dashboard", "version": "1.0.0", "description": "Web Dashboard for Al Shams Door Factory Management System", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "keywords": ["react", "typescript", "vite", "dashboard", "door-factory"], "author": "Al Shams Door Factory", "license": "PROPRIETARY", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "antd": "^5.8.4", "@ant-design/icons": "^5.2.5", "@ant-design/charts": "^2.0.3", "axios": "^1.4.0", "dayjs": "^1.11.9", "react-query": "^3.39.3", "zustand": "^4.4.1", "react-hook-form": "^7.45.4", "@hookform/resolvers": "^3.1.1", "yup": "^1.2.0", "react-beautiful-dnd": "^13.1.1", "react-dropzone": "^14.2.3", "react-image-gallery": "^1.3.0", "qrcode.react": "^3.1.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "xlsx": "^0.18.5", "socket.io-client": "^4.7.2", "react-helmet-async": "^1.3.0", "react-error-boundary": "^4.0.11"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-beautiful-dnd": "^13.1.4", "@types/qrcode.react": "^1.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.1", "@vitest/ui": "^0.34.1", "jsdom": "^22.1.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^14.4.3", "msw": "^1.2.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}