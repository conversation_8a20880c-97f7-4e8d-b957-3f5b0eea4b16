import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Delete existing colors
  await knex('colors').del();

  // Insert default colors
  const colors = [
    {
      id: knex.raw('gen_random_uuid()'),
      name: '<PERSON>',
      name_ar: 'أبيض',
      hex_code: '#FFFFFF',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Cream',
      name_ar: 'كريمي',
      hex_code: '#F5F5DC',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: '<PERSON> <PERSON>',
      name_ar: 'بني فاتح',
      hex_code: '#D2B48C',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: '<PERSON>',
      name_ar: 'بني',
      hex_code: '#8B4513',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: '<PERSON>',
      name_ar: 'بني داكن',
      hex_code: '#654321',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Black',
      name_ar: 'أسود',
      hex_code: '#000000',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Gray',
      name_ar: 'رمادي',
      hex_code: '#808080',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Light Gray',
      name_ar: 'رمادي فاتح',
      hex_code: '#D3D3D3',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Beige',
      name_ar: 'بيج',
      hex_code: '#F5F5DC',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Mahogany',
      name_ar: 'ماهوجني',
      hex_code: '#C04000',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Oak',
      name_ar: 'بلوط',
      hex_code: '#D2691E',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Cherry',
      name_ar: 'كرزي',
      hex_code: '#DE3163',
      is_active: true
    }
  ];

  await knex('colors').insert(colors);

  console.log(`✅ Inserted ${colors.length} colors successfully`);
}
