# Al Shams Door Factory - Web Dashboard

لوحة تحكم ويب لنظام إدارة معمل الشمس لصناعة الأبواب مبنية باستخدام React.js و TypeScript.

## 🚀 البدء السريع

### المتطلبات
- Node.js 18+
- npm 9+

### التثبيت

```bash
# تثبيت المكتبات
npm install

# نسخ ملف البيئة
cp .env.example .env

# تشغيل التطبيق في وضع التطوير
npm run dev
```

التطبيق سيكون متاحاً على `http://localhost:3001`

## 📋 الأوامر المتاحة

### التطوير
```bash
npm run dev          # تشغيل خادم التطوير
npm run build        # بناء التطبيق للإنتاج
npm run preview      # معاينة البناء
npm run serve        # تشغيل الخادم للإنتاج
```

### الجودة والاختبار
```bash
npm run lint         # فحص الكود
npm run lint:fix     # إصلاح مشاكل الكود
npm run type-check   # فحص أنواع TypeScript
npm test             # تشغيل الاختبارات
npm run test:ui      # واجهة الاختبارات
npm run test:coverage # تقرير التغطية
```

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - لغة البرمجة
- **Vite** - أداة البناء والتطوير
- **Ant Design** - مكتبة المكونات
- **React Router** - التنقل
- **Zustand** - إدارة الحالة
- **React Query** - إدارة البيانات
- **Axios** - طلبات HTTP

### المميزات
- ✅ دعم كامل للغة العربية (RTL)
- ✅ تصميم متجاوب
- ✅ نظام مصادقة آمن
- ✅ إدارة حالة متقدمة
- ✅ معالجة أخطاء شاملة
- ✅ تحميل تدريجي للصفحات
- ✅ تحسين الأداء

## 📁 هيكل المشروع

```
src/
├── components/      # المكونات المشتركة
├── layouts/         # تخطيطات الصفحات
├── pages/           # صفحات التطبيق
│   ├── auth/        # صفحات المصادقة
│   ├── dashboard/   # لوحة التحكم
│   ├── orders/      # إدارة الطلبات
│   ├── users/       # إدارة المستخدمين
│   ├── settings/    # الإعدادات
│   └── reports/     # التقارير
├── services/        # خدمات API
├── stores/          # إدارة الحالة
├── styles/          # ملفات التنسيق
├── utils/           # وظائف مساعدة
└── types/           # تعريفات الأنواع
```

## 🔐 نظام المصادقة

### تسجيل الدخول
يدعم التطبيق طريقتين لتسجيل الدخول:
1. **كلمة المرور**: رقم الهاتف + كلمة المرور
2. **رمز التحقق**: رقم الهاتف + OTP

### بيانات تجريبية
```
الهاتف: +966501234567
كلمة المرور: admin123456
```

### إدارة الجلسات
- تجديد تلقائي للرموز المميزة
- حفظ حالة المصادقة محلياً
- تسجيل خروج آمن

## 🎨 التصميم والواجهة

### الألوان الأساسية
- **الأساسي**: #1890ff (أزرق)
- **النجاح**: #52c41a (أخضر)
- **التحذير**: #faad14 (أصفر)
- **الخطر**: #ff4d4f (أحمر)

### الخطوط
- **الأساسي**: Cairo (عربي)
- **الثانوي**: -apple-system, BlinkMacSystemFont

### الاستجابة
- **Desktop**: > 1200px
- **Tablet**: 768px - 1200px
- **Mobile**: < 768px

## 📊 الصفحات المتاحة

### لوحة التحكم
- إحصائيات عامة
- الطلبات الأخيرة
- تقدم الإنتاج
- إجراءات سريعة

### إدارة الطلبات
- قائمة الطلبات
- تفاصيل الطلب
- إنشاء طلب جديد
- تتبع المراحل

### إدارة المستخدمين (للمدير)
- قائمة المستخدمين
- إضافة مستخدم جديد
- تعديل الصلاحيات

### الإعدادات (للمدير)
- إدارة الألوان
- إدارة النقوش
- إعدادات الأسعار

### التقارير
- تقارير المبيعات
- تقارير الإنتاج
- إحصائيات الأداء

## 🔧 التكوين

### متغيرات البيئة
```bash
VITE_API_URL=http://localhost:3000/api/v1
VITE_APP_NAME=معمل الشمس لصناعة الأبواب
VITE_APP_VERSION=1.0.0
```

### إعدادات Vite
- Proxy للـ API
- تحسين البناء
- تقسيم الكود
- دعم TypeScript

## 🚀 النشر

### Docker
```bash
# بناء الصورة
docker build -t alshams-web .

# تشغيل الحاوية
docker run -p 80:80 alshams-web
```

### البناء للإنتاج
```bash
npm run build
```

الملفات ستكون في مجلد `dist/`

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
npm test
```

### تقرير التغطية
```bash
npm run test:coverage
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بـ API**
   - تأكد من تشغيل الخادم الخلفي
   - تحقق من `VITE_API_URL`

2. **مشاكل في التصميم**
   - امسح ذاكرة التخزين المؤقت
   - أعد تشغيل خادم التطوير

3. **أخطاء TypeScript**
   - تشغيل `npm run type-check`
   - تحديث تعريفات الأنواع

### وضع التطوير
```bash
# تفعيل أدوات التطوير
VITE_ENABLE_DEVTOOLS=true

# تفعيل البيانات التجريبية
VITE_ENABLE_MOCK_DATA=true
```

## 📞 الدعم

للحصول على المساعدة:
- البريد الإلكتروني: <EMAIL>
- الوثائق: `/docs`
- المشاكل: GitHub Issues

## 📝 الترخيص

هذا المشروع مملوك لمعمل الشمس لصناعة الأبواب.
