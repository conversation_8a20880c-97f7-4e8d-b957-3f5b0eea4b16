import React from 'react'
import { Result, Button } from 'antd'
import { useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate()

  return (
    <>
      <Helmet>
        <title>الصفحة غير موجودة - معمل الشمس</title>
      </Helmet>

      <Result
        status="404"
        title="404"
        subTitle="عذراً، الصفحة التي تبحث عنها غير موجودة"
        extra={
          <Button type="primary" onClick={() => navigate('/dashboard')}>
            العودة للرئيسية
          </Button>
        }
      />
    </>
  )
}

export default NotFoundPage
