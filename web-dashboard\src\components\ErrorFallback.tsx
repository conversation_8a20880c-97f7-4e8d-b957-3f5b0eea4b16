import React from 'react'
import { Result, But<PERSON>, <PERSON>po<PERSON>, Card, Space } from 'antd'
import { ReloadOutlined, HomeOutlined, BugOutlined } from '@ant-design/icons'
import { FallbackProps } from 'react-error-boundary'

const { Paragraph, Text } = Typography

interface ErrorFallbackProps extends FallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary 
}) => {
  const isDevelopment = import.meta.env.DEV

  const handleGoHome = () => {
    window.location.href = '/'
  }

  const handleReload = () => {
    window.location.reload()
  }

  const handleReportError = () => {
    // In a real app, you would send this to your error reporting service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    console.error('Error Report:', errorReport)
    
    // You could also copy to clipboard or open email client
    navigator.clipboard?.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        alert('تم نسخ تفاصيل الخطأ إلى الحافظة')
      })
      .catch(() => {
        alert('فشل في نسخ تفاصيل الخطأ')
      })
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      padding: '20px',
      background: '#f0f2f5'
    }}>
      <Card style={{ maxWidth: 600, width: '100%' }}>
        <Result
          status="error"
          title="حدث خطأ غير متوقع"
          subTitle="نعتذر، حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى."
          extra={
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Space wrap>
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={resetErrorBoundary}
                >
                  إعادة المحاولة
                </Button>
                <Button 
                  icon={<HomeOutlined />}
                  onClick={handleGoHome}
                >
                  العودة للرئيسية
                </Button>
                <Button 
                  icon={<ReloadOutlined />}
                  onClick={handleReload}
                >
                  إعادة تحميل الصفحة
                </Button>
              </Space>
              
              {isDevelopment && (
                <Button 
                  type="dashed"
                  icon={<BugOutlined />}
                  onClick={handleReportError}
                  size="small"
                >
                  نسخ تفاصيل الخطأ
                </Button>
              )}
            </Space>
          }
        />
        
        {isDevelopment && (
          <Card 
            title="تفاصيل الخطأ (وضع التطوير فقط)" 
            size="small"
            style={{ marginTop: 20 }}
            type="inner"
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>رسالة الخطأ:</Text>
                <Paragraph 
                  code 
                  copyable
                  style={{ 
                    background: '#f5f5f5', 
                    padding: '8px',
                    borderRadius: '4px',
                    marginTop: '8px'
                  }}
                >
                  {error.message}
                </Paragraph>
              </div>
              
              {error.stack && (
                <div>
                  <Text strong>تتبع المكدس:</Text>
                  <Paragraph 
                    code 
                    copyable
                    style={{ 
                      background: '#f5f5f5', 
                      padding: '8px',
                      borderRadius: '4px',
                      marginTop: '8px',
                      fontSize: '12px',
                      maxHeight: '200px',
                      overflow: 'auto'
                    }}
                  >
                    {error.stack}
                  </Paragraph>
                </div>
              )}
            </Space>
          </Card>
        )}
        
        <Card 
          size="small"
          style={{ marginTop: 20 }}
          type="inner"
        >
          <Space direction="vertical" size="small">
            <Text type="secondary">
              إذا استمر هذا الخطأ، يرجى:
            </Text>
            <ul style={{ margin: 0, paddingRight: 20 }}>
              <li>التأكد من اتصالك بالإنترنت</li>
              <li>مسح ذاكرة التخزين المؤقت للمتصفح</li>
              <li>التواصل مع الدعم الفني</li>
            </ul>
          </Space>
        </Card>
      </Card>
    </div>
  )
}

export default ErrorFallback
