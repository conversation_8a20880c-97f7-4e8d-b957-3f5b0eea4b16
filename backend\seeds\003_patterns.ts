import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Delete existing patterns
  await knex('patterns').del();

  // Insert default patterns
  const patterns = [
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Plain',
      name_ar: 'سادة',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: '<PERSON> Grain',
      name_ar: 'عروق خشب',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Modern Lines',
      name_ar: 'خطوط عصرية',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Classic Panel',
      name_ar: 'لوحة كلاسيكية',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Geometric',
      name_ar: 'هندسي',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Raised Panel',
      name_ar: 'لوحة مرفوعة',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Shaker Style',
      name_ar: 'نمط شيكر',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Traditional',
      name_ar: 'تقليدي',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Contemporary',
      name_ar: 'معاصر',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Minimalist',
      name_ar: 'بسيط',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Carved Design',
      name_ar: 'تصميم منحوت',
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Glass Insert',
      name_ar: 'إدراج زجاجي',
      is_active: true
    }
  ];

  await knex('patterns').insert(patterns);

  console.log(`✅ Inserted ${patterns.length} patterns successfully`);
}
