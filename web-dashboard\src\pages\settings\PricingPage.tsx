import React, { useState } from 'react'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  Row,
  Col,
  message,
  InputNumber,
  Select,
  Statistic,
  Tag
} from 'antd'
import {
  SearchOutlined,
  EditOutlined,
  DollarOutlined,
  CalculatorOutlined,
  SaveOutlined
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'

const { Title, Text } = Typography
const { Option } = Select

interface PriceConfig {
  key: string
  id: string
  color_id: string
  color_name: string
  color_name_ar: string
  pattern_id: string
  pattern_name: string
  pattern_name_ar: string
  base_price_per_m2: number
  last_updated: string
}

const PricingPage: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [modalVisible, setModalVisible] = useState(false)
  const [calculatorVisible, setCalculatorVisible] = useState(false)
  const [editingPrice, setEditingPrice] = useState<PriceConfig | null>(null)
  const [form] = Form.useForm()
  const [calculatorForm] = Form.useForm()

  // دالة لتنسيق العملة العراقية
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} د.ع`
  }

  // بيانات تجريبية للأسعار
  const [priceConfigs, setPriceConfigs] = useState<PriceConfig[]>([
    {
      key: '1',
      id: '1',
      color_id: '1',
      color_name: 'White',
      color_name_ar: 'أبيض',
      pattern_id: '1',
      pattern_name: 'Plain',
      pattern_name_ar: 'سادة',
      base_price_per_m2: 120000,
      last_updated: '2024-01-15'
    },
    {
      key: '2',
      id: '2',
      color_id: '1',
      color_name: 'White',
      color_name_ar: 'أبيض',
      pattern_id: '2',
      pattern_name: 'Wood Grain',
      pattern_name_ar: 'عروق خشب',
      base_price_per_m2: 150000,
      last_updated: '2024-01-15'
    },
    {
      key: '3',
      id: '3',
      color_id: '2',
      color_name: 'Light Brown',
      color_name_ar: 'بني فاتح',
      pattern_id: '1',
      pattern_name: 'Plain',
      pattern_name_ar: 'سادة',
      base_price_per_m2: 132000,
      last_updated: '2024-01-15'
    },
    {
      key: '4',
      id: '4',
      color_id: '2',
      color_name: 'Light Brown',
      color_name_ar: 'بني فاتح',
      pattern_id: '2',
      pattern_name: 'Wood Grain',
      pattern_name_ar: 'عروق خشب',
      base_price_per_m2: 165000,
      last_updated: '2024-01-15'
    },
    {
      key: '5',
      id: '5',
      color_id: '3',
      color_name: 'Dark Brown',
      color_name_ar: 'بني داكن',
      pattern_id: '1',
      pattern_name: 'Plain',
      pattern_name_ar: 'سادة',
      base_price_per_m2: 144000,
      last_updated: '2024-01-15'
    },
    {
      key: '6',
      id: '6',
      color_id: '3',
      color_name: 'Dark Brown',
      color_name_ar: 'بني داكن',
      pattern_id: '3',
      pattern_name: 'Classic Panel',
      pattern_name_ar: 'لوحة كلاسيكية',
      base_price_per_m2: 192000,
      last_updated: '2024-01-15'
    }
  ])

  const handleEditPrice = (priceConfig: PriceConfig) => {
    setEditingPrice(priceConfig)
    form.setFieldsValue({
      base_price_per_m2: priceConfig.base_price_per_m2
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      if (editingPrice) {
        setPriceConfigs(priceConfigs.map(config =>
          config.id === editingPrice.id
            ? {
                ...config,
                base_price_per_m2: values.base_price_per_m2,
                last_updated: new Date().toISOString().split('T')[0]
              }
            : config
        ))
        message.success('تم تحديث السعر بنجاح')
      }

      setModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleCalculatePrice = (values: any) => {
    const { width, height, color_pattern } = values
    const area = (width * height) / 10000 // تحويل إلى متر مربع

    const priceConfig = priceConfigs.find(config =>
      `${config.color_id}-${config.pattern_id}` === color_pattern
    )

    if (priceConfig) {
      const totalPrice = Math.round(area * priceConfig.base_price_per_m2)

      message.success({
        content: (
          <div>
            <div><strong>نتيجة الحساب:</strong></div>
            <div>المساحة: {area.toFixed(2)} متر مربع</div>
            <div>السعر للمتر: {formatCurrency(priceConfig.base_price_per_m2)}</div>
            <div><strong>السعر الإجمالي: {formatCurrency(totalPrice)}</strong></div>
          </div>
        ),
        duration: 10
      })
    }
  }

  const columns = [
    {
      title: 'اللون',
      key: 'color',
      render: (record: PriceConfig) => (
        <Tag color="blue">{record.color_name_ar}</Tag>
      )
    },
    {
      title: 'النقش',
      key: 'pattern',
      render: (record: PriceConfig) => (
        <Tag color="purple">{record.pattern_name_ar}</Tag>
      )
    },
    {
      title: 'السعر للمتر المربع',
      dataIndex: 'base_price_per_m2',
      key: 'base_price_per_m2',
      render: (price: number) => (
        <Text strong style={{ color: '#1890ff', fontSize: 16 }}>
          {formatCurrency(price)}
        </Text>
      ),
      sorter: (a: PriceConfig, b: PriceConfig) => a.base_price_per_m2 - b.base_price_per_m2
    },
    {
      title: 'آخر تحديث',
      dataIndex: 'last_updated',
      key: 'last_updated'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: PriceConfig) => (
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => handleEditPrice(record)}
        >
          تعديل السعر
        </Button>
      )
    }
  ]

  const filteredPrices = priceConfigs.filter(config => {
    const matchesSearch = !searchText ||
      config.color_name_ar.toLowerCase().includes(searchText.toLowerCase()) ||
      config.pattern_name_ar.toLowerCase().includes(searchText.toLowerCase()) ||
      config.color_name.toLowerCase().includes(searchText.toLowerCase()) ||
      config.pattern_name.toLowerCase().includes(searchText.toLowerCase())

    return matchesSearch
  })

  // إحصائيات الأسعار
  const priceStats = {
    total: priceConfigs.length,
    minPrice: Math.min(...priceConfigs.map(p => p.base_price_per_m2)),
    maxPrice: Math.max(...priceConfigs.map(p => p.base_price_per_m2)),
    avgPrice: Math.round(priceConfigs.reduce((sum, p) => sum + p.base_price_per_m2, 0) / priceConfigs.length)
  }

  // خيارات اللون والنقش للحاسبة
  const colorPatternOptions = priceConfigs.map(config => ({
    value: `${config.color_id}-${config.pattern_id}`,
    label: `${config.color_name_ar} - ${config.pattern_name_ar}`,
    price: config.base_price_per_m2
  }))

  return (
    <>
      <Helmet>
        <title>إدارة الأسعار - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={2} style={{ margin: 0 }}>
              إدارة الأسعار
            </Title>
            <Button
              type="primary"
              icon={<CalculatorOutlined />}
              onClick={() => setCalculatorVisible(true)}
            >
              حاسبة الأسعار
            </Button>
          </div>

          {/* إحصائيات الأسعار */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="إجمالي التكوينات"
                  value={priceStats.total}
                  prefix={<DollarOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="أقل سعر"
                  value={priceStats.minPrice}
                  formatter={(value) => formatCurrency(Number(value))}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="أعلى سعر"
                  value={priceStats.maxPrice}
                  formatter={(value) => formatCurrency(Number(value))}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="متوسط السعر"
                  value={priceStats.avgPrice}
                  formatter={(value) => formatCurrency(Number(value))}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          {/* Search */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={12}>
                <Input
                  placeholder="البحث في الأسعار..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
            </Row>
          </Card>
        </div>

        <Card>
          <Table
            columns={columns}
            dataSource={filteredPrices}
            loading={loading}
            pagination={{
              total: filteredPrices.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} من ${total} تكوين سعر`
            }}
          />
        </Card>

        {/* Modal for Edit Price */}
        <Modal
          title={`تعديل سعر: ${editingPrice?.color_name_ar} - ${editingPrice?.pattern_name_ar}`}
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={null}
          width={400}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Form.Item
              name="base_price_per_m2"
              label="السعر للمتر المربع (د.ع)"
              rules={[
                { required: true, message: 'يرجى إدخال السعر' },
                { type: 'number', min: 1000, message: 'السعر يجب أن يكون أكبر من 1000' }
              ]}
            >
              <InputNumber
                placeholder="120000"
                style={{ width: '100%' }}
                formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                min={1000}
                max={1000000}
              />
            </Form.Item>

            <div style={{ textAlign: 'left', marginTop: 24 }}>
              <Space>
                <Button onClick={() => setModalVisible(false)}>
                  إلغاء
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                >
                  حفظ
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>

        {/* Price Calculator Modal */}
        <Modal
          title="حاسبة الأسعار"
          open={calculatorVisible}
          onCancel={() => setCalculatorVisible(false)}
          footer={null}
          width={500}
        >
          <Form
            form={calculatorForm}
            layout="vertical"
            onFinish={handleCalculatePrice}
          >
            <Row gutter={16}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="width"
                  label="العرض (سم)"
                  rules={[{ required: true, message: 'مطلوب' }]}
                >
                  <InputNumber
                    placeholder="80"
                    style={{ width: '100%' }}
                    min={60}
                    max={120}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="height"
                  label="الارتفاع (سم)"
                  rules={[{ required: true, message: 'مطلوب' }]}
                >
                  <InputNumber
                    placeholder="200"
                    style={{ width: '100%' }}
                    min={180}
                    max={250}
                  />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item
                  name="color_pattern"
                  label="اللون والنقش"
                  rules={[{ required: true, message: 'يرجى اختيار اللون والنقش' }]}
                >
                  <Select placeholder="اختر اللون والنقش">
                    {colorPatternOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>{option.label}</span>
                          <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                            {formatCurrency(option.price)}
                          </span>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <div style={{ textAlign: 'center', marginTop: 24 }}>
              <Button
                type="primary"
                htmlType="submit"
                icon={<CalculatorOutlined />}
                size="large"
              >
                احسب السعر
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
    </>
  )
}

export default PricingPage
