import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Spin } from 'antd'
import { Helmet } from 'react-helmet-async'

import { useAuthStore } from './stores/authStore'
import AuthLayout from './layouts/AuthLayout'
import DashboardLayout from './layouts/DashboardLayout'
import ProtectedRoute from './components/ProtectedRoute'
import LoadingSpinner from './components/LoadingSpinner'

// Lazy load pages
const LoginPage = React.lazy(() => import('./pages/auth/LoginPage'))
const DashboardPage = React.lazy(() => import('./pages/dashboard/DashboardPage'))
const OrdersPage = React.lazy(() => import('./pages/orders/OrdersPage'))
const OrderDetailsPage = React.lazy(() => import('./pages/orders/OrderDetailsPage'))
const CreateOrderPage = React.lazy(() => import('./pages/orders/CreateOrderPage'))
const UsersPage = React.lazy(() => import('./pages/users/UsersPage'))
const ColorsPage = React.lazy(() => import('./pages/settings/ColorsPage'))
const PatternsPage = React.lazy(() => import('./pages/settings/PatternsPage'))
const PricingPage = React.lazy(() => import('./pages/settings/PricingPage'))
const ReportsPage = React.lazy(() => import('./pages/reports/ReportsPage'))
const ProfilePage = React.lazy(() => import('./pages/profile/ProfilePage'))
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'))

function App() {
  const { isAuthenticated, isLoading } = useAuthStore()

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <>
      <Helmet>
        <title>معمل الشمس لصناعة الأبواب - لوحة التحكم</title>
        <meta name="description" content="نظام إدارة معمل الشمس لصناعة الأبواب" />
      </Helmet>

      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          {/* Public Routes */}
          <Route path="/auth/*" element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <AuthLayout />
          }>
            <Route path="login" element={<LoginPage />} />
            <Route index element={<Navigate to="login" replace />} />
          </Route>

          {/* Protected Routes */}
          <Route path="/*" element={
            <ProtectedRoute>
              <DashboardLayout />
            </ProtectedRoute>
          }>
            {/* Dashboard */}
            <Route path="dashboard" element={<DashboardPage />} />
            
            {/* Orders */}
            <Route path="orders" element={<OrdersPage />} />
            <Route path="orders/create" element={<CreateOrderPage />} />
            <Route path="orders/:id" element={<OrderDetailsPage />} />
            
            {/* Users */}
            <Route path="users" element={<UsersPage />} />
            
            {/* Settings */}
            <Route path="settings/colors" element={<ColorsPage />} />
            <Route path="settings/patterns" element={<PatternsPage />} />
            <Route path="settings/pricing" element={<PricingPage />} />
            
            {/* Reports */}
            <Route path="reports" element={<ReportsPage />} />
            
            {/* Profile */}
            <Route path="profile" element={<ProfilePage />} />
            
            {/* Default redirect */}
            <Route index element={<Navigate to="dashboard" replace />} />
            
            {/* 404 */}
            <Route path="*" element={<NotFoundPage />} />
          </Route>

          {/* Root redirect */}
          <Route path="/" element={
            <Navigate to={isAuthenticated ? "/dashboard" : "/auth/login"} replace />
          } />
        </Routes>
      </Suspense>
    </>
  )
}

export default App
