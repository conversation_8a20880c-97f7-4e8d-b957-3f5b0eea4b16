﻿import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, Typography, Table, Button, Space, Input, Select, Tag, Row, Col, Statistic } from 'antd'
import { PlusOutlined, SearchOutlined, EyeOutlined, EditOutlined, ReloadOutlined } from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'
import dayjs from 'dayjs'

const { Title } = Typography

const OrdersPage: React.FC = () => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [orders, setOrders] = useState<any[]>([])

  const formatCurrency = (amount: number) => {
    return ${amount.toLocaleString()} د.ع
  }

  useEffect(() => {
    const loadOrders = () => {
      const mockOrders = [
        {
          key: '1',
          id: 'ORD-001',
          client: 'أحم<PERSON> محمد الجبوري',
          phone: '07901234567',
          status: 'in_design',
          items: 2,
          total: 1500000,
          location: 'بغداد الكرادة',
          created_at: '2024-01-15',
          updated_at: '2024-01-15'
        },
        {
          key: '2',
          id: 'ORD-002',
          client: 'فاطمة حسن البصري',
          phone: '07802345678',
          status: 'in_cutting',
          items: 1,
          total: 800000,
          location: 'البصرة العشار',
          created_at: '2024-01-14',
          updated_at: '2024-01-14'
        }
      ]

      const savedOrders = JSON.parse(localStorage.getItem('orders') || '[]')
      const formattedSavedOrders = savedOrders.map((order: any) => ({
        key: order.id.toString(),
        id: order.order_id,
        client: order.client_name,
        phone: order.phone_number,
        status: order.status,
        items: order.items?.length || 1,
        total: order.total,
        location: order.location,
        created_at: dayjs(order.created_at).format('YYYY-MM-DD'),
        updated_at: dayjs(order.updated_at).format('YYYY-MM-DD')
      }))
      
      setOrders([...formattedSavedOrders, ...mockOrders])
    }

    loadOrders()
    
    const handleFocus = () => {
      loadOrders()
    }
    
    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [])

  const getStatusTag = (status: string) => {
    const statusMap = {
      'new': { color: 'blue', text: 'طلب جديد' },
      'in_design': { color: 'purple', text: 'في التصميم' },
      'in_cutting': { color: 'cyan', text: 'في التفصال' },
      'ready_for_delivery': { color: 'green', text: 'جاهز للتوصيل' },
      'delivered': { color: 'success', text: 'تم التوصيل' },
      'cancelled': { color: 'error', text: 'ملغي' }
    }
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
  }

  const columns = [
    {
      title: 'رقم الطلب',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <Button type="link" onClick={() => navigate(/orders/)}>
          {text}
        </Button>
      )
    },
    {
      title: 'العميل',
      dataIndex: 'client',
      key: 'client'
    },
    {
      title: 'الهاتف',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone: string) => (
        <span dir="ltr" style={{ fontFamily: 'monospace' }}>{phone}</span>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: 'عدد الأبواب',
      dataIndex: 'items',
      key: 'items',
      align: 'center' as const
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total',
      key: 'total',
      render: (amount: number) => formatCurrency(amount),
      align: 'left' as const
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location'
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(/orders/)}
          >
            عرض
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => navigate(/orders//edit)}
          >
            تعديل
          </Button>
        </Space>
      )
    }
  ]

  const filteredOrders = orders.filter(order => {
    const matchesSearch = !searchText || 
      order.client.toLowerCase().includes(searchText.toLowerCase()) ||
      order.id.toLowerCase().includes(searchText.toLowerCase()) ||
      order.phone.includes(searchText)
    
    const matchesStatus = !statusFilter || order.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const stats = {
    total: orders.length,
    pending: orders.filter(o => !['delivered', 'cancelled'].includes(o.status)).length,
    completed: orders.filter(o => o.status === 'delivered').length,
    revenue: orders.reduce((sum, order) => sum + order.total, 0)
  }

  return (
    <>
      <Helmet>
        <title>الطلبات - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={2} style={{ margin: 0 }}>
              إدارة الطلبات
            </Title>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => window.location.reload()}
              >
                تحديث
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/orders/create')}
              >
                طلب جديد
              </Button>
            </Space>
          </div>

          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="إجمالي الطلبات"
                  value={stats.total}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="قيد التنفيذ"
                  value={stats.pending}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="مكتملة"
                  value={stats.completed}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="إجمالي الإيرادات"
                  value={stats.revenue}
                  suffix="د.ع"
                  valueStyle={{ color: '#722ed1' }}
                  formatter={(value) => ${Number(value).toLocaleString()}}
                />
              </Card>
            </Col>
          </Row>

          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={12}>
                <Input
                  placeholder="البحث في الطلبات..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="حالة الطلب"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  style={{ width: '100%' }}
                  allowClear
                >
                  <Select.Option value="new">طلب جديد</Select.Option>
                  <Select.Option value="in_design">في التصميم</Select.Option>
                  <Select.Option value="in_cutting">في التفصال</Select.Option>
                  <Select.Option value="ready_for_delivery">جاهز للتوصيل</Select.Option>
                  <Select.Option value="delivered">تم التوصيل</Select.Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </div>

        <Card>
          <Table
            columns={columns}
            dataSource={filteredOrders}
            loading={loading}
            pagination={{
              total: filteredOrders.length,
              pageSize: 10,
              showSizeChanger: window.innerWidth > 768,
              showQuickJumper: window.innerWidth > 768,
              showTotal: (total, range) =>
                ${range[0]}- من  طلب,
              size: window.innerWidth <= 768 ? 'small' : 'default'
            }}
            scroll={{ x: 1200 }}
            size={window.innerWidth <= 768 ? 'small' : 'default'}
          />
        </Card>
      </div>
    </>
  )
}

export default OrdersPage
