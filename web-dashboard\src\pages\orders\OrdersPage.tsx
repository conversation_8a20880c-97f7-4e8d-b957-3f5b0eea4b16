import React, { useState } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  DatePicker, 
  Typography,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  FilterOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import dayjs from 'dayjs'

const { Title } = Typography
const { RangePicker } = DatePicker

const OrdersPage: React.FC = () => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null)

  // دالة لتنسيق العملة العراقية
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} د.ع`
  }

  // Mock data
  const orders = [
    {
      key: '1',
      id: 'ORD-001',
      client: 'أحمد محمد الجبوري',
      phone: '07901234567',
      status: 'in_design',
      items: 2,
      total: 1500000,
      location: 'بغداد، الكرادة',
      created_at: '2024-01-15',
      updated_at: '2024-01-15'
    },
    {
      key: '2',
      id: 'ORD-002',
      client: 'فاطمة حسن البصري',
      phone: '07802345678',
      status: 'in_cutting',
      items: 1,
      total: 800000,
      location: 'البصرة، العشار',
      created_at: '2024-01-14',
      updated_at: '2024-01-14'
    },
    {
      key: '3',
      id: 'ORD-003',
      client: 'محمد علي الكربلائي',
      phone: '07703456789',
      status: 'ready_for_delivery',
      items: 3,
      total: 2200000,
      location: 'كربلاء، الحسين',
      created_at: '2024-01-13',
      updated_at: '2024-01-15'
    },
    {
      key: '4',
      id: 'ORD-004',
      client: 'سارة أحمد النجفي',
      phone: '07604567890',
      status: 'delivered',
      items: 1,
      total: 950000,
      location: 'النجف، الكوفة',
      created_at: '2024-01-12',
      updated_at: '2024-01-14'
    }
  ]

  const getStatusTag = (status: string) => {
    const statusMap = {
      'new': { color: 'blue', text: 'طلب جديد' },
      'awaiting_measurement': { color: 'orange', text: 'في انتظار القياس' },
      'in_design': { color: 'purple', text: 'في التصميم' },
      'in_cutting': { color: 'cyan', text: 'في التفصال' },
      'in_pressing': { color: 'magenta', text: 'في الكبس' },
      'in_vacuum': { color: 'lime', text: 'في الفاكيوم' },
      'edge_cutting': { color: 'gold', text: 'قطع الحواف' },
      'tape_application': { color: 'volcano', text: 'لصق الشريط' },
      'packaging': { color: 'geekblue', text: 'التغليف' },
      'ready_for_delivery': { color: 'green', text: 'جاهز للتوصيل' },
      'delivered': { color: 'success', text: 'تم التوصيل' },
      'cancelled': { color: 'error', text: 'ملغي' }
    }
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
  }

  const columns = [
    {
      title: 'رقم الطلب',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <Button type="link" onClick={() => navigate(`/orders/${text}`)}>
          {text}
        </Button>
      )
    },
    {
      title: 'العميل',
      dataIndex: 'client',
      key: 'client'
    },
    {
      title: 'الهاتف',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone: string) => (
        <span dir="ltr" style={{ fontFamily: 'monospace' }}>{phone}</span>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: 'عدد الأبواب',
      dataIndex: 'items',
      key: 'items',
      align: 'center' as const
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total',
      key: 'total',
      render: (amount: number) => formatCurrency(amount),
      align: 'left' as const
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location'
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/orders/${record.id}`)}
          >
            عرض
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => navigate(`/orders/${record.id}/edit`)}
          >
            تعديل
          </Button>
        </Space>
      )
    }
  ]

  const statusOptions = [
    { label: 'جميع الحالات', value: '' },
    { label: 'طلب جديد', value: 'new' },
    { label: 'في انتظار القياس', value: 'awaiting_measurement' },
    { label: 'في التصميم', value: 'in_design' },
    { label: 'في التفصال', value: 'in_cutting' },
    { label: 'في الكبس', value: 'in_pressing' },
    { label: 'جاهز للتوصيل', value: 'ready_for_delivery' },
    { label: 'تم التوصيل', value: 'delivered' }
  ]

  const filteredOrders = orders.filter(order => {
    const matchesSearch = !searchText || 
      order.client.toLowerCase().includes(searchText.toLowerCase()) ||
      order.id.toLowerCase().includes(searchText.toLowerCase()) ||
      order.phone.includes(searchText)
    
    const matchesStatus = !statusFilter || order.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const stats = {
    total: orders.length,
    pending: orders.filter(o => !['delivered', 'cancelled'].includes(o.status)).length,
    completed: orders.filter(o => o.status === 'delivered').length,
    revenue: orders.reduce((sum, order) => sum + order.total, 0)
  }

  return (
    <>
      <Helmet>
        <title>الطلبات - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={2} style={{ margin: 0 }}>
              إدارة الطلبات
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/orders/create')}
            >
              طلب جديد
            </Button>
          </div>

          {/* Statistics */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="إجمالي الطلبات"
                  value={stats.total}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="قيد التنفيذ"
                  value={stats.pending}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="مكتملة"
                  value={stats.completed}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card size="small">
                <Statistic
                  title="إجمالي الإيرادات"
                  value={stats.revenue}
                  suffix="د.ع"
                  valueStyle={{ color: '#722ed1' }}
                  formatter={(value) => `${Number(value).toLocaleString()}`}
                />
              </Card>
            </Col>
          </Row>

          {/* Filters */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={8}>
                <Input
                  placeholder="البحث في الطلبات..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="حالة الطلب"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  options={statusOptions}
                  style={{ width: '100%' }}
                  allowClear
                />
              </Col>
              <Col xs={24} sm={8}>
                <RangePicker
                  placeholder={['من تاريخ', 'إلى تاريخ']}
                  value={dateRange}
                  onChange={setDateRange}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col xs={24} sm={2}>
                <Button
                  icon={<FilterOutlined />}
                  onClick={() => {
                    setSearchText('')
                    setStatusFilter('')
                    setDateRange(null)
                  }}
                >
                  مسح
                </Button>
              </Col>
            </Row>
          </Card>
        </div>

        <Card>
          <Table
            columns={columns}
            dataSource={filteredOrders}
            loading={loading}
            pagination={{
              total: filteredOrders.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `${range[0]}-${range[1]} من ${total} طلب`
            }}
            scroll={{ x: 1200 }}
          />
        </Card>
      </div>
    </>
  )
}

export default OrdersPage
