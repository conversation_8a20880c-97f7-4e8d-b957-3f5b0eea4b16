import React from 'react'
import { useParams } from 'react-router-dom'
import { Card, Typography, Result } from 'antd'
import { Helmet } from 'react-helmet-async'

const { Title } = Typography

const OrderDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()

  return (
    <>
      <Helmet>
        <title>تفاصيل الطلب {id} - معمل الشمس</title>
      </Helmet>

      <div>
        <Title level={2}>تفاصيل الطلب {id}</Title>
        
        <Card>
          <Result
            status="info"
            title="صفحة قيد التطوير"
            subTitle={`تفاصيل الطلب ${id} ستكون متاحة قريباً`}
          />
        </Card>
      </div>
    </>
  )
}

export default OrderDetailsPage
