import React from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Card,
  Typography,
  Row,
  Col,
  Descriptions,
  Tag,
  Button,
  Space,
  Timeline,
  Image,
  Divider,
  Statistic
} from 'antd'
import {
  ArrowLeftOutlined,
  EditOutlined,
  PrinterOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  UserOutlined
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'

const { Title, Text } = Typography

const OrderDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  // دالة لتنسيق العملة العراقية
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} د.ع`
  }

  // بيانات تجريبية للطلب
  const orderData = {
    id: id || 'ORD-001',
    client: 'أحم<PERSON> محمد الجبوري',
    phone: '07901234567',
    email: '<EMAIL>',
    status: 'in_design',
    location: 'بغداد، الكرادة، شارع الكرادة الداخل',
    coordinates: { lat: 33.3152, lng: 44.3661 },
    total: 1500000,
    created_at: '2024-01-15',
    updated_at: '2024-01-15',
    notes: 'يرجى التواصل قبل التوصيل بيوم واحد',
    items: [
      {
        id: '1',
        width: 80,
        height: 200,
        color: 'بني داكن',
        pattern: 'عروق خشب',
        opening_type: 'فتحة زجاج',
        price: 750000,
        notes: 'باب غرفة النوم الرئيسية'
      },
      {
        id: '2',
        width: 70,
        height: 200,
        color: 'أبيض',
        pattern: 'سادة',
        opening_type: 'بدون فتحة',
        price: 750000,
        notes: 'باب الحمام'
      }
    ],
    timeline: [
      {
        status: 'new',
        title: 'تم إنشاء الطلب',
        description: 'تم استلام الطلب من العميل',
        time: '2024-01-15 10:00',
        completed: true
      },
      {
        status: 'awaiting_measurement',
        title: 'في انتظار القياس',
        description: 'تم تحديد موعد القياس',
        time: '2024-01-15 14:00',
        completed: true
      },
      {
        status: 'in_design',
        title: 'في التصميم',
        description: 'جاري العمل على التصميم',
        time: '2024-01-16 09:00',
        completed: false,
        current: true
      },
      {
        status: 'in_cutting',
        title: 'في التفصال',
        description: 'سيتم البدء بالتفصال',
        time: '',
        completed: false
      }
    ]
  }

  const getStatusTag = (status: string) => {
    const statusMap = {
      'new': { color: 'blue', text: 'طلب جديد' },
      'awaiting_measurement': { color: 'orange', text: 'في انتظار القياس' },
      'in_design': { color: 'purple', text: 'في التصميم' },
      'in_cutting': { color: 'cyan', text: 'في التفصال' },
      'in_pressing': { color: 'magenta', text: 'في الكبس' },
      'ready_for_delivery': { color: 'green', text: 'جاهز للتوصيل' },
      'delivered': { color: 'success', text: 'تم التوصيل' }
    }

    const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
  }

  return (
    <>
      <Helmet>
        <title>تفاصيل الطلب {id} - معمل الشمس</title>
      </Helmet>

      <div>
        {/* Header */}
        <div style={{ marginBottom: 24 }}>
          <Space style={{ marginBottom: 16 }}>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/orders')}
            >
              العودة للطلبات
            </Button>
          </Space>

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Title level={2} style={{ margin: 0 }}>
                تفاصيل الطلب {orderData.id}
              </Title>
              <Text type="secondary">
                تم الإنشاء في {orderData.created_at}
              </Text>
            </div>

            <Space>
              <Button icon={<EditOutlined />}>
                تعديل
              </Button>
              <Button icon={<PrinterOutlined />}>
                طباعة
              </Button>
            </Space>
          </div>
        </div>

        <Row gutter={[16, 16]}>
          {/* معلومات الطلب */}
          <Col xs={24} lg={16}>
            <Card title="معلومات الطلب" style={{ marginBottom: 16 }}>
              <Descriptions column={2} bordered>
                <Descriptions.Item label="رقم الطلب" span={1}>
                  <Text strong>{orderData.id}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="الحالة" span={1}>
                  {getStatusTag(orderData.status)}
                </Descriptions.Item>
                <Descriptions.Item label="اسم العميل" span={1}>
                  <Space>
                    <UserOutlined />
                    {orderData.client}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="رقم الهاتف" span={1}>
                  <Space>
                    <PhoneOutlined />
                    <Text dir="ltr">{orderData.phone}</Text>
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="الموقع" span={2}>
                  <Space>
                    <EnvironmentOutlined />
                    {orderData.location}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="تاريخ الإنشاء" span={1}>
                  <Space>
                    <CalendarOutlined />
                    {orderData.created_at}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="آخر تحديث" span={1}>
                  <Space>
                    <CalendarOutlined />
                    {orderData.updated_at}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="المبلغ الإجمالي" span={2}>
                  <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
                    {formatCurrency(orderData.total)}
                  </Text>
                </Descriptions.Item>
                {orderData.notes && (
                  <Descriptions.Item label="ملاحظات" span={2}>
                    {orderData.notes}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>

            {/* تفاصيل الأبواب */}
            <Card title="تفاصيل الأبواب">
              {orderData.items.map((item, index) => (
                <Card
                  key={item.id}
                  type="inner"
                  title={`الباب ${index + 1}`}
                  style={{ marginBottom: index < orderData.items.length - 1 ? 16 : 0 }}
                >
                  <Row gutter={16}>
                    <Col xs={24} sm={12}>
                      <Descriptions column={1} size="small">
                        <Descriptions.Item label="الأبعاد">
                          {item.width} × {item.height} سم
                        </Descriptions.Item>
                        <Descriptions.Item label="اللون">
                          {item.color}
                        </Descriptions.Item>
                        <Descriptions.Item label="النقش">
                          {item.pattern}
                        </Descriptions.Item>
                      </Descriptions>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Descriptions column={1} size="small">
                        <Descriptions.Item label="نوع الفتحة">
                          {item.opening_type}
                        </Descriptions.Item>
                        <Descriptions.Item label="السعر">
                          <Text strong>{formatCurrency(item.price)}</Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="ملاحظات">
                          {item.notes}
                        </Descriptions.Item>
                      </Descriptions>
                    </Col>
                  </Row>
                </Card>
              ))}
            </Card>
          </Col>

          {/* الشريط الجانبي */}
          <Col xs={24} lg={8}>
            {/* إحصائيات سريعة */}
            <Card title="ملخص الطلب" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="عدد الأبواب"
                    value={orderData.items.length}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="المبلغ الإجمالي"
                    value={orderData.total}
                    formatter={(value) => formatCurrency(Number(value))}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
              </Row>
            </Card>

            {/* تتبع المراحل */}
            <Card title="تتبع مراحل الإنتاج">
              <Timeline>
                {orderData.timeline.map((stage, index) => (
                  <Timeline.Item
                    key={index}
                    color={stage.completed ? 'green' : stage.current ? 'blue' : 'gray'}
                    dot={stage.current ? <div style={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: '#1890ff',
                      animation: 'pulse 2s infinite'
                    }} /> : undefined}
                  >
                    <div>
                      <Text strong>{stage.title}</Text>
                      <br />
                      <Text type="secondary">{stage.description}</Text>
                      {stage.time && (
                        <>
                          <br />
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {stage.time}
                          </Text>
                        </>
                      )}
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          </Col>
        </Row>
      </div>

      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </>
  )
}

export default OrderDetailsPage
