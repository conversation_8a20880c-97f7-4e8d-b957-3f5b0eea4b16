import React, { useState } from 'react'
import { Form, Input, Button, Alert, Space, Divider, Typography } from 'antd'
import { UserOutlined, LockOutlined, PhoneOutlined, SafetyOutlined } from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../../stores/authStore'

const { Text, Link } = Typography

interface LoginFormData {
  phone_number: string
  password?: string
  otp?: string
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [otpMode, setOtpMode] = useState(false)
  const [otpSent, setOtpSent] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { login, error, clearError } = useAuthStore()

  const from = location.state?.from?.pathname || '/dashboard'

  const handleLogin = async (values: LoginFormData) => {
    try {
      setLoading(true)
      clearError()
      
      await login(values)
      navigate(from, { replace: true })
    } catch (error) {
      console.error('Login failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSendOTP = async () => {
    try {
      const phoneNumber = form.getFieldValue('phone_number')
      if (!phoneNumber) {
        form.setFields([{
          name: 'phone_number',
          errors: ['يرجى إدخال رقم الهاتف']
        }])
        return
      }

      setLoading(true)
      // Here you would call the sendOTP API
      // await authService.sendOTP({ phone_number: phoneNumber })
      
      setOtpSent(true)
      setOtpMode(true)
    } catch (error) {
      console.error('Send OTP failed:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      {error && (
        <Alert
          message="خطأ في تسجيل الدخول"
          description={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 24 }}
        />
      )}

      {location.state?.error && (
        <Alert
          message={location.state.error}
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Form
        form={form}
        name="login"
        onFinish={handleLogin}
        layout="vertical"
        size="large"
      >
        <Form.Item
          name="phone_number"
          label="رقم الهاتف"
          rules={[
            { required: true, message: 'يرجى إدخال رقم الهاتف' }
          ]}
          initialValue="admin"
        >
          <Input
            prefix={<PhoneOutlined />}
            placeholder="admin"
          />
        </Form.Item>

        {!otpMode ? (
          <Form.Item
            name="password"
            label="كلمة المرور"
            rules={[
              { required: true, message: 'يرجى إدخال كلمة المرور' }
            ]}
            initialValue="123"
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="123"
            />
          </Form.Item>
        ) : (
          <Form.Item
            name="otp"
            label="رمز التحقق"
            rules={[
              { required: true, message: 'يرجى إدخال رمز التحقق' },
              { len: 6, message: 'رمز التحقق يجب أن يكون 6 أرقام' }
            ]}
          >
            <Input
              prefix={<SafetyOutlined />}
              placeholder="123456"
              maxLength={6}
              dir="ltr"
              style={{ textAlign: 'center', fontSize: 18, letterSpacing: 4 }}
            />
          </Form.Item>
        )}

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            style={{ height: 48 }}
          >
            {otpMode ? 'تأكيد رمز التحقق' : 'تسجيل الدخول'}
          </Button>
        </Form.Item>

        <Divider>أو</Divider>

        <Space direction="vertical" style={{ width: '100%' }}>
          {!otpMode ? (
            <Button
              type="default"
              icon={<PhoneOutlined />}
              onClick={handleSendOTP}
              loading={loading}
              block
            >
              تسجيل الدخول برمز التحقق
            </Button>
          ) : (
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Button
                type="link"
                onClick={() => {
                  setOtpMode(false)
                  setOtpSent(false)
                  form.resetFields(['otp'])
                }}
              >
                العودة لكلمة المرور
              </Button>
              
              <Button
                type="link"
                onClick={handleSendOTP}
                loading={loading}
                disabled={loading}
              >
                إعادة إرسال الرمز
              </Button>
            </Space>
          )}
        </Space>

        {otpSent && (
          <Alert
            message="تم إرسال رمز التحقق"
            description="تحقق من رسائلك النصية وأدخل الرمز المكون من 6 أرقام"
            type="success"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Form>

      <div style={{ marginTop: 24, textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: 12 }}>
          بيانات تجريبية للاختبار:
        </Text>
        <br />
        <Text code style={{ fontSize: 14, color: '#1890ff' }}>
          اسم المستخدم: admin | كلمة المرور: 123
        </Text>
      </div>
    </div>
  )
}

export default LoginPage
