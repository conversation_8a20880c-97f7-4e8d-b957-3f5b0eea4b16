import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create colors table
  await knex.schema.createTable('colors', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 100).notNullable();
    table.string('name_ar', 100).notNullable();
    table.string('hex_code', 7); // For color codes like #FFFFFF
    table.text('image_url');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['is_active']);
    table.index(['name']);
    table.index(['name_ar']);
  });

  // Create patterns table
  await knex.schema.createTable('patterns', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 100).notNullable();
    table.string('name_ar', 100).notNullable();
    table.text('image_url');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['is_active']);
    table.index(['name']);
    table.index(['name_ar']);
  });

  // Create prices_config table
  await knex.schema.createTable('prices_config', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('color_id').notNullable().references('id').inTable('colors').onDelete('CASCADE');
    table.uuid('pattern_id').notNullable().references('id').inTable('patterns').onDelete('CASCADE');
    table.decimal('base_price_per_m2', 8, 2).notNullable();
    table.timestamp('last_updated').defaultTo(knex.fn.now());
    table.timestamps(true, true);
    
    // Unique constraint for color-pattern combination
    table.unique(['color_id', 'pattern_id']);
    
    // Indexes
    table.index(['color_id']);
    table.index(['pattern_id']);
    table.index(['base_price_per_m2']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('prices_config');
  await knex.schema.dropTableIfExists('patterns');
  await knex.schema.dropTableIfExists('colors');
}
