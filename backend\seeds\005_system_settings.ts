import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Delete existing system settings
  await knex('system_settings').del();

  // Insert default system settings
  const settings = [
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'company_name',
      value: 'معمل الشمس لصناعة الأبواب',
      type: 'string',
      description: 'اسم الشركة',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'company_name_en',
      value: 'Al Shams Door Factory',
      type: 'string',
      description: 'Company name in English',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'company_phone',
      value: '+************',
      type: 'string',
      description: 'رقم هاتف الشركة',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'company_email',
      value: '<EMAIL>',
      type: 'string',
      description: 'البريد الإلكتروني للشركة',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'company_address',
      value: 'الرياض، المملكة العربية السعودية',
      type: 'string',
      description: 'عنوان الشركة',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'default_door_width',
      value: '80',
      type: 'number',
      description: 'العرض الافتراضي للباب (سم)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'default_door_height',
      value: '200',
      type: 'number',
      description: 'الارتفاع الافتراضي للباب (سم)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'min_door_width',
      value: '60',
      type: 'number',
      description: 'الحد الأدنى لعرض الباب (سم)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'max_door_width',
      value: '120',
      type: 'number',
      description: 'الحد الأقصى لعرض الباب (سم)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'min_door_height',
      value: '180',
      type: 'number',
      description: 'الحد الأدنى لارتفاع الباب (سم)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'max_door_height',
      value: '250',
      type: 'number',
      description: 'الحد الأقصى لارتفاع الباب (سم)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'currency',
      value: 'SAR',
      type: 'string',
      description: 'العملة المستخدمة',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'tax_rate',
      value: '15',
      type: 'number',
      description: 'معدل الضريبة (%)',
      is_public: false
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'delivery_fee',
      value: '50',
      type: 'number',
      description: 'رسوم التوصيل (ريال)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'installation_fee',
      value: '100',
      type: 'number',
      description: 'رسوم التركيب (ريال)',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'otp_expiry_minutes',
      value: '5',
      type: 'number',
      description: 'مدة انتهاء صلاحية رمز التحقق (دقائق)',
      is_public: false
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'max_otp_attempts',
      value: '3',
      type: 'number',
      description: 'الحد الأقصى لمحاولات رمز التحقق',
      is_public: false
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'order_auto_cancel_hours',
      value: '24',
      type: 'number',
      description: 'إلغاء الطلب تلقائياً بعد (ساعة)',
      is_public: false
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'notification_enabled',
      value: 'true',
      type: 'boolean',
      description: 'تفعيل الإشعارات',
      is_public: false
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'sms_enabled',
      value: 'true',
      type: 'boolean',
      description: 'تفعيل الرسائل النصية',
      is_public: false
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'email_enabled',
      value: 'true',
      type: 'boolean',
      description: 'تفعيل البريد الإلكتروني',
      is_public: false
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'working_hours',
      value: JSON.stringify({
        sunday: { start: '08:00', end: '17:00', enabled: true },
        monday: { start: '08:00', end: '17:00', enabled: true },
        tuesday: { start: '08:00', end: '17:00', enabled: true },
        wednesday: { start: '08:00', end: '17:00', enabled: true },
        thursday: { start: '08:00', end: '17:00', enabled: true },
        friday: { start: '14:00', end: '17:00', enabled: true },
        saturday: { start: '08:00', end: '17:00', enabled: true }
      }),
      type: 'json',
      description: 'ساعات العمل',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'maintenance_mode',
      value: 'false',
      type: 'boolean',
      description: 'وضع الصيانة',
      is_public: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      key: 'app_version',
      value: '1.0.0',
      type: 'string',
      description: 'إصدار التطبيق',
      is_public: true
    }
  ];

  await knex('system_settings').insert(settings);

  console.log(`✅ Inserted ${settings.length} system settings successfully`);
}
