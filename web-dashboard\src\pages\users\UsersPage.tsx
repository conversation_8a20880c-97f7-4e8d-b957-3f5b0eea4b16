import React, { useState } from 'react'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Switch,
  message,
  Popconfirm,
  Avatar
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  LockOutlined
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'
import { UserRole } from '../../types'

const { Title } = Typography
const { Option } = Select

interface User {
  key: string
  id: string
  name: string
  phone_number: string
  email?: string
  role: UserRole
  is_active: boolean
  created_at: string
  last_login?: string
}

const UsersPage: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalVisible, setModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [form] = Form.useForm()

  // بيانات تجريبية للمستخدمين
  const [users, setUsers] = useState<User[]>([
    {
      key: '1',
      id: '1',
      name: 'المدير العام',
      phone_number: 'admin',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      is_active: true,
      created_at: '2024-01-01',
      last_login: '2024-01-15'
    },
    {
      key: '2',
      id: '2',
      name: 'أحمد محمد - قياسات',
      phone_number: '07901234567',
      email: '<EMAIL>',
      role: UserRole.MEASUREMENT,
      is_active: true,
      created_at: '2024-01-02',
      last_login: '2024-01-14'
    },
    {
      key: '3',
      id: '3',
      name: 'سعد علي - مصمم',
      phone_number: '07802345678',
      email: '<EMAIL>',
      role: UserRole.DESIGNER,
      is_active: true,
      created_at: '2024-01-03',
      last_login: '2024-01-13'
    },
    {
      key: '4',
      id: '4',
      name: 'محمد عبدالله - تفصال',
      phone_number: '07703456789',
      role: UserRole.CUTTING,
      is_active: true,
      created_at: '2024-01-04'
    },
    {
      key: '5',
      id: '5',
      name: 'خالد أحمد - كبس',
      phone_number: '07604567890',
      role: UserRole.PRESSING,
      is_active: false,
      created_at: '2024-01-05'
    },
    {
      key: '6',
      id: '6',
      name: 'فاطمة حسن - عميل',
      phone_number: '07505678901',
      email: '<EMAIL>',
      role: UserRole.CLIENT,
      is_active: true,
      created_at: '2024-01-10',
      last_login: '2024-01-15'
    }
  ])

  const getRoleTag = (role: UserRole) => {
    const roleMap = {
      [UserRole.ADMIN]: { color: 'red', text: 'مدير' },
      [UserRole.CLIENT]: { color: 'blue', text: 'عميل' },
      [UserRole.MEASUREMENT]: { color: 'orange', text: 'قياسات' },
      [UserRole.DESIGNER]: { color: 'purple', text: 'مصمم' },
      [UserRole.CUTTING]: { color: 'cyan', text: 'تفصال' },
      [UserRole.PRESSING]: { color: 'magenta', text: 'كبس' },
      [UserRole.VACUUM]: { color: 'lime', text: 'فاكيوم' },
      [UserRole.EDGE_CUTTING]: { color: 'gold', text: 'حواف' },
      [UserRole.TAPE_APPLICATION]: { color: 'volcano', text: 'شريط' },
      [UserRole.PACKAGING]: { color: 'geekblue', text: 'تغليف' },
      [UserRole.DELIVERY]: { color: 'green', text: 'توصيل' }
    }

    const roleInfo = roleMap[role] || { color: 'default', text: role }
    return <Tag color={roleInfo.color}>{roleInfo.text}</Tag>
  }

  const handleAddUser = () => {
    setEditingUser(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    form.setFieldsValue(user)
    setModalVisible(true)
  }

  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId))
    message.success('تم حذف المستخدم بنجاح')
  }

  const handleToggleStatus = (userId: string) => {
    setUsers(users.map(user =>
      user.id === userId
        ? { ...user, is_active: !user.is_active }
        : user
    ))
    message.success('تم تحديث حالة المستخدم')
  }

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      if (editingUser) {
        // تحديث مستخدم موجود
        setUsers(users.map(user =>
          user.id === editingUser.id
            ? { ...user, ...values }
            : user
        ))
        message.success('تم تحديث المستخدم بنجاح')
      } else {
        // إضافة مستخدم جديد
        const newUser: User = {
          ...values,
          key: Date.now().toString(),
          id: Date.now().toString(),
          created_at: new Date().toISOString().split('T')[0],
          is_active: true
        }
        setUsers([...users, newUser])
        message.success('تم إضافة المستخدم بنجاح')
      }

      setModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      title: 'المستخدم',
      key: 'user',
      render: (record: User) => (
        <Space>
          <Avatar
            icon={<UserOutlined />}
            style={{ backgroundColor: record.is_active ? '#1890ff' : '#999' }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name}</div>
            <div style={{ fontSize: 12, color: '#666' }}>
              {record.phone_number}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => email || '-'
    },
    {
      title: 'الدور',
      dataIndex: 'role',
      key: 'role',
      render: (role: UserRole) => getRoleTag(role)
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (is_active: boolean, record: User) => (
        <Switch
          checked={is_active}
          onChange={() => handleToggleStatus(record.id)}
          checkedChildren="مفعل"
          unCheckedChildren="معطل"
        />
      )
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at'
    },
    {
      title: 'آخر دخول',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (last_login: string) => last_login || 'لم يدخل بعد'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: User) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
          >
            تعديل
          </Button>
          {record.role !== UserRole.ADMIN && (
            <Popconfirm
              title="هل أنت متأكد من حذف هذا المستخدم؟"
              onConfirm={() => handleDeleteUser(record.id)}
              okText="نعم"
              cancelText="لا"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              >
                حذف
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  const roleOptions = [
    { label: 'جميع الأدوار', value: '' },
    { label: 'مدير', value: UserRole.ADMIN },
    { label: 'عميل', value: UserRole.CLIENT },
    { label: 'قياسات', value: UserRole.MEASUREMENT },
    { label: 'مصمم', value: UserRole.DESIGNER },
    { label: 'تفصال', value: UserRole.CUTTING },
    { label: 'كبس', value: UserRole.PRESSING },
    { label: 'فاكيوم', value: UserRole.VACUUM },
    { label: 'حواف', value: UserRole.EDGE_CUTTING },
    { label: 'شريط', value: UserRole.TAPE_APPLICATION },
    { label: 'تغليف', value: UserRole.PACKAGING },
    { label: 'توصيل', value: UserRole.DELIVERY }
  ]

  const statusOptions = [
    { label: 'جميع الحالات', value: '' },
    { label: 'مفعل', value: 'active' },
    { label: 'معطل', value: 'inactive' }
  ]

  const filteredUsers = users.filter(user => {
    const matchesSearch = !searchText ||
      user.name.toLowerCase().includes(searchText.toLowerCase()) ||
      user.phone_number.includes(searchText) ||
      (user.email && user.email.toLowerCase().includes(searchText.toLowerCase()))

    const matchesRole = !roleFilter || user.role === roleFilter
    const matchesStatus = !statusFilter ||
      (statusFilter === 'active' && user.is_active) ||
      (statusFilter === 'inactive' && !user.is_active)

    return matchesSearch && matchesRole && matchesStatus
  })

  return (
    <>
      <Helmet>
        <title>المستخدمين - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={2} style={{ margin: 0 }}>
              إدارة المستخدمين
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddUser}
            >
              مستخدم جديد
            </Button>
          </div>

          {/* Filters */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={8}>
                <Input
                  placeholder="البحث في المستخدمين..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="الدور"
                  value={roleFilter}
                  onChange={setRoleFilter}
                  options={roleOptions}
                  style={{ width: '100%' }}
                  allowClear
                />
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="الحالة"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  options={statusOptions}
                  style={{ width: '100%' }}
                  allowClear
                />
              </Col>
            </Row>
          </Card>
        </div>

        <Card>
          <Table
            columns={columns}
            dataSource={filteredUsers}
            loading={loading}
            pagination={{
              total: filteredUsers.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} من ${total} مستخدم`
            }}
            scroll={{ x: 1000 }}
          />
        </Card>

        {/* Modal for Add/Edit User */}
        <Modal
          title={editingUser ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={null}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Row gutter={16}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="name"
                  label="الاسم الكامل"
                  rules={[{ required: true, message: 'يرجى إدخال الاسم' }]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="أدخل الاسم الكامل"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="phone_number"
                  label="رقم الهاتف"
                  rules={[
                    { required: true, message: 'يرجى إدخال رقم الهاتف' },
                    { pattern: /^07[0-9]{9}$/, message: 'رقم الهاتف غير صحيح' }
                  ]}
                >
                  <Input
                    prefix={<PhoneOutlined />}
                    placeholder="07901234567"
                    dir="ltr"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="email"
                  label="البريد الإلكتروني"
                  rules={[{ type: 'email', message: 'البريد الإلكتروني غير صحيح' }]}
                >
                  <Input
                    prefix={<MailOutlined />}
                    placeholder="<EMAIL>"
                    dir="ltr"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="role"
                  label="الدور"
                  rules={[{ required: true, message: 'يرجى اختيار الدور' }]}
                >
                  <Select placeholder="اختر الدور">
                    {roleOptions.slice(1).map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              {!editingUser && (
                <Col xs={24}>
                  <Form.Item
                    name="password"
                    label="كلمة المرور"
                    rules={[
                      { required: true, message: 'يرجى إدخال كلمة المرور' },
                      { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' }
                    ]}
                  >
                    <Input.Password
                      prefix={<LockOutlined />}
                      placeholder="كلمة المرور"
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <div style={{ textAlign: 'left', marginTop: 24 }}>
              <Space>
                <Button onClick={() => setModalVisible(false)}>
                  إلغاء
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  {editingUser ? 'تحديث' : 'إضافة'}
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>
      </div>
    </>
  )
}

export default UsersPage
