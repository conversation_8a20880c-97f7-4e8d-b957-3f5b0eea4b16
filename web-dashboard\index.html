<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="نظام إدارة معمل الشمس لصناعة الأبواب" />
    <meta name="keywords" content="أبواب, معمل, إدارة, الشمس" />
    <meta name="author" content="Al Shams Door Factory" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Arabic fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Ant Design RTL styles -->
    <style>
      * {
        font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      body {
        margin: 0;
        padding: 0;
        direction: rtl;
        text-align: right;
      }
      
      /* Loading spinner */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f0f2f5;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 20px;
        color: #666;
        font-size: 16px;
      }

      /* Mobile optimizations */
      @media (max-width: 768px) {
        .ant-table-wrapper {
          overflow-x: auto;
        }

        .ant-table {
          min-width: 600px;
        }

        .ant-card-body {
          padding: 16px !important;
        }

        .ant-form-item {
          margin-bottom: 16px !important;
        }

        .ant-btn {
          height: 40px !important;
        }

        .ant-input, .ant-select-selector {
          height: 40px !important;
        }

        .ant-statistic-title {
          font-size: 12px !important;
        }

        .ant-statistic-content {
          font-size: 18px !important;
        }
      }
    </style>
    
    <title>معمل الشمس لصناعة الأبواب - لوحة التحكم</title>
  </head>
  <body>
    <div id="root">
      <!-- Loading screen -->
      <div class="loading-container">
        <div style="text-align: center;">
          <div class="loading-spinner"></div>
          <div class="loading-text">جاري تحميل النظام...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
