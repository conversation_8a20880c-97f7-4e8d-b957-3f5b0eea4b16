import { Knex } from 'knex';
import bcrypt from 'bcryptjs';

export async function seed(knex: Knex): Promise<void> {
  // Only run in development environment
  if (process.env.NODE_ENV === 'production') {
    console.log('⚠️ Skipping sample data in production environment');
    return;
  }

  // Delete existing sample data (except admin)
  await knex('order_stages_log').del();
  await knex('order_items').del();
  await knex('orders').del();
  await knex('users').whereNot('role', 'admin').del();

  const passwordHash = await bcrypt.hash('password123', 12);

  // Insert sample technicians
  const technicians = [
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'أحمد محمد - قياسات',
      phone_number: '+************',
      role: 'measurement',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'سعد علي - مصمم',
      phone_number: '+************',
      role: 'designer',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'محمد عبدالله - تفصال',
      phone_number: '+************',
      role: 'cutting',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'خالد أحمد - كبس',
      phone_number: '+************',
      role: 'pressing',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'عبدالرحمن سعد - فاكيوم',
      phone_number: '+************',
      role: 'vacuum',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'فهد محمد - حواف',
      phone_number: '+************',
      role: 'edge_cutting',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'عبدالعزيز علي - شريط',
      phone_number: '+************',
      role: 'tape_application',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'ناصر عبدالله - تغليف',
      phone_number: '+************',
      role: 'packaging',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'عبدالله خالد - توصيل',
      phone_number: '+************',
      role: 'delivery',
      password_hash: passwordHash,
      is_active: true
    }
  ];

  await knex('users').insert(technicians);

  // Insert sample clients
  const clients = [
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'عبدالرحمن الأحمد',
      phone_number: '+************',
      role: 'client',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'فاطمة السعد',
      phone_number: '+************',
      role: 'client',
      password_hash: passwordHash,
      is_active: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'محمد العلي',
      phone_number: '+************',
      role: 'client',
      password_hash: passwordHash,
      is_active: true
    }
  ];

  await knex('users').insert(clients);

  // Get inserted users for reference
  const allUsers = await knex('users').select('id', 'role');
  const clientUsers = allUsers.filter(u => u.role === 'client');
  const measurementUser = allUsers.find(u => u.role === 'measurement');

  // Get colors and patterns for sample orders
  const colors = await knex('colors').select('id').limit(3);
  const patterns = await knex('patterns').select('id').limit(3);
  const priceConfigs = await knex('prices_config')
    .select('color_id', 'pattern_id', 'base_price_per_m2')
    .limit(9);

  // Insert sample orders
  const orders = [];
  const orderItems = [];

  for (let i = 0; i < clientUsers.length; i++) {
    const client = clientUsers[i];
    const orderId = knex.raw('gen_random_uuid()');
    
    // Create order
    orders.push({
      id: orderId,
      client_id: client.id,
      status: i === 0 ? 'new' : i === 1 ? 'in_design' : 'delivered',
      location: `الرياض، حي ${['النرجس', 'العليا', 'الملقا'][i]}`,
      location_coordinates: JSON.stringify({
        latitude: 24.7136 + (Math.random() - 0.5) * 0.1,
        longitude: 46.6753 + (Math.random() - 0.5) * 0.1
      }),
      total_price: 0, // Will be calculated by trigger
      notes: `طلب تجريبي رقم ${i + 1}`,
      created_at: knex.raw(`CURRENT_TIMESTAMP - INTERVAL '${i + 1} days'`),
      updated_at: knex.raw(`CURRENT_TIMESTAMP - INTERVAL '${i + 1} days'`)
    });

    // Create order items (1-3 doors per order)
    const numDoors = Math.floor(Math.random() * 3) + 1;
    for (let j = 0; j < numDoors; j++) {
      const priceConfig = priceConfigs[Math.floor(Math.random() * priceConfigs.length)];
      const width = 80 + Math.floor(Math.random() * 20); // 80-100 cm
      const height = 200 + Math.floor(Math.random() * 20); // 200-220 cm
      const area = (width * height) / 10000; // Convert to m²
      const price = Math.round(area * priceConfig.base_price_per_m2 * 100) / 100;

      orderItems.push({
        id: knex.raw('gen_random_uuid()'),
        order_id: orderId,
        width: width,
        height: height,
        color_id: priceConfig.color_id,
        pattern_id: priceConfig.pattern_id,
        opening_type: ['فتحة زجاج', 'تهوية', 'بدون فتحة'][Math.floor(Math.random() * 3)],
        price: price,
        notes: `باب رقم ${j + 1}`,
        created_at: knex.raw(`CURRENT_TIMESTAMP - INTERVAL '${i + 1} days'`),
        updated_at: knex.raw(`CURRENT_TIMESTAMP - INTERVAL '${i + 1} days'`)
      });
    }
  }

  await knex('orders').insert(orders);
  await knex('order_items').insert(orderItems);

  // Insert sample order stages log for the completed order
  if (measurementUser) {
    const completedOrder = await knex('orders')
      .select('id')
      .where('status', 'delivered')
      .first();

    if (completedOrder) {
      const completedOrderItems = await knex('order_items')
        .select('id')
        .where('order_id', completedOrder.id);

      const stages = [
        'new',
        'awaiting_measurement', 
        'in_design',
        'in_cutting',
        'in_pressing',
        'in_vacuum',
        'edge_cutting',
        'tape_application',
        'packaging',
        'ready_for_delivery',
        'delivered'
      ];

      const stageLog = [];
      for (const item of completedOrderItems) {
        for (let k = 0; k < stages.length; k++) {
          const stage = stages[k];
          const startTime = knex.raw(`CURRENT_TIMESTAMP - INTERVAL '${stages.length - k} hours'`);
          const endTime = k < stages.length - 1 ? 
            knex.raw(`CURRENT_TIMESTAMP - INTERVAL '${stages.length - k - 1} hours'`) : 
            knex.raw('CURRENT_TIMESTAMP');

          stageLog.push({
            id: knex.raw('gen_random_uuid()'),
            order_item_id: item.id,
            stage_name: stage,
            technician_id: measurementUser.id,
            start_time: startTime,
            end_time: endTime,
            notes: `تم إنجاز مرحلة ${stage}`,
            created_at: startTime,
            updated_at: endTime
          });
        }
      }

      await knex('order_stages_log').insert(stageLog);
    }
  }

  console.log('✅ Sample data inserted successfully');
  console.log(`👥 Inserted ${technicians.length} technicians`);
  console.log(`👤 Inserted ${clients.length} clients`);
  console.log(`📦 Inserted ${orders.length} sample orders`);
  console.log(`🚪 Inserted ${orderItems.length} sample doors`);
  console.log('🔑 Default password for all users: password123');
}
