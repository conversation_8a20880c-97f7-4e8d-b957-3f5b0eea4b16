import React, { useState } from 'react'
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  DatePicker,
  Select,
  Button,
  Space,
  Table,
  Progress,
  Tag
} from 'antd'
import {
  BarChartOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  TruckOutlined,
  ClockCircleOutlined,
  DownloadOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

const ReportsPage: React.FC = () => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>([
    dayjs().subtract(30, 'days'),
    dayjs()
  ])
  const [reportType, setReportType] = useState('overview')

  // دالة لتنسيق العملة العراقية
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} د.ع`
  }

  // بيانات تجريبية للتقارير
  const overviewStats = {
    totalRevenue: 15750000,
    totalOrders: 89,
    completedOrders: 67,
    pendingOrders: 22,
    totalClients: 45,
    averageOrderValue: 1770000,
    deliveryRate: 95.5,
    productionTime: 7.2
  }

  const monthlyData = [
    { month: 'يناير', orders: 25, revenue: 4200000, completed: 23 },
    { month: 'فبراير', orders: 32, revenue: 5600000, completed: 30 },
    { month: 'مارس', orders: 28, revenue: 4800000, completed: 26 },
    { month: 'أبريل', orders: 35, revenue: 6100000, completed: 33 },
    { month: 'مايو', orders: 29, revenue: 5050000, completed: 27 }
  ]

  const topProducts = [
    {
      id: '1',
      color: 'بني داكن',
      pattern: 'عروق خشب',
      orders: 23,
      revenue: 3450000,
      percentage: 21.9
    },
    {
      id: '2',
      color: 'أبيض',
      pattern: 'سادة',
      orders: 18,
      revenue: 2160000,
      percentage: 13.7
    },
    {
      id: '3',
      color: 'بني فاتح',
      pattern: 'لوحة كلاسيكية',
      orders: 15,
      revenue: 2400000,
      percentage: 15.2
    },
    {
      id: '4',
      color: 'رمادي',
      pattern: 'خطوط عصرية',
      orders: 12,
      revenue: 1680000,
      percentage: 10.7
    }
  ]

  const productionStages = [
    { stage: 'القياسات', completed: 45, total: 50, percentage: 90 },
    { stage: 'التصميم', completed: 38, total: 45, percentage: 84.4 },
    { stage: 'التفصال', completed: 35, total: 38, percentage: 92.1 },
    { stage: 'الكبس', completed: 32, total: 35, percentage: 91.4 },
    { stage: 'الفاكيوم', completed: 30, total: 32, percentage: 93.8 },
    { stage: 'التغليف', completed: 28, total: 30, percentage: 93.3 },
    { stage: 'التوصيل', completed: 25, total: 28, percentage: 89.3 }
  ]

  const clientsData = [
    {
      key: '1',
      name: 'أحمد محمد الجبوري',
      orders: 5,
      totalSpent: 7500000,
      lastOrder: '2024-01-15',
      status: 'VIP'
    },
    {
      key: '2',
      name: 'فاطمة حسن البصري',
      orders: 3,
      totalSpent: 4200000,
      lastOrder: '2024-01-12',
      status: 'عادي'
    },
    {
      key: '3',
      name: 'محمد علي الكربلائي',
      orders: 4,
      totalSpent: 6800000,
      lastOrder: '2024-01-10',
      status: 'VIP'
    }
  ]

  const handleExportReport = () => {
    // هنا سيتم تصدير التقرير
    console.log('Exporting report...')
  }

  const handlePrintReport = () => {
    // هنا سيتم طباعة التقرير
    window.print()
  }

  const clientColumns = [
    {
      title: 'اسم العميل',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'عدد الطلبات',
      dataIndex: 'orders',
      key: 'orders',
      align: 'center' as const
    },
    {
      title: 'إجمالي المبلغ',
      dataIndex: 'totalSpent',
      key: 'totalSpent',
      render: (amount: number) => formatCurrency(amount)
    },
    {
      title: 'آخر طلب',
      dataIndex: 'lastOrder',
      key: 'lastOrder'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'VIP' ? 'gold' : 'blue'}>
          {status}
        </Tag>
      )
    }
  ]

  return (
    <>
      <Helmet>
        <title>التقارير - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={2} style={{ margin: 0 }}>
              التقارير والإحصائيات
            </Title>
            <Space>
              <Button icon={<DownloadOutlined />} onClick={handleExportReport}>
                تصدير
              </Button>
              <Button icon={<PrinterOutlined />} onClick={handlePrintReport}>
                طباعة
              </Button>
            </Space>
          </div>

          {/* Filters */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={8}>
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  placeholder={['من تاريخ', 'إلى تاريخ']}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  value={reportType}
                  onChange={setReportType}
                  style={{ width: '100%' }}
                >
                  <Option value="overview">نظرة عامة</Option>
                  <Option value="sales">تقرير المبيعات</Option>
                  <Option value="production">تقرير الإنتاج</Option>
                  <Option value="clients">تقرير العملاء</Option>
                </Select>
              </Col>
            </Row>
          </Card>
        </div>

        {/* Overview Statistics */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="إجمالي الإيرادات"
                value={overviewStats.totalRevenue}
                prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="إجمالي الطلبات"
                value={overviewStats.totalOrders}
                prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="الطلبات المكتملة"
                value={overviewStats.completedOrders}
                prefix={<TruckOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="إجمالي العملاء"
                value={overviewStats.totalClients}
                prefix={<UserOutlined style={{ color: '#fa8c16' }} />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* Monthly Performance */}
          <Col xs={24} lg={12}>
            <Card title="الأداء الشهري" extra={<BarChartOutlined />}>
              <div style={{ height: 300 }}>
                {monthlyData.map((month, index) => (
                  <div key={index} style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                      <Text>{month.month}</Text>
                      <Text strong>{formatCurrency(month.revenue)}</Text>
                    </div>
                    <Progress
                      percent={Math.round((month.revenue / 6100000) * 100)}
                      strokeColor="#1890ff"
                      size="small"
                    />
                    <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                      {month.orders} طلب - {month.completed} مكتمل
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>

          {/* Top Products */}
          <Col xs={24} lg={12}>
            <Card title="المنتجات الأكثر طلباً">
              <div style={{ height: 300 }}>
                {topProducts.map((product, index) => (
                  <div key={product.id} style={{ marginBottom: 20 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                      <Text strong>{product.color} - {product.pattern}</Text>
                      <Text>{product.orders} طلب</Text>
                    </div>
                    <Progress
                      percent={product.percentage}
                      strokeColor={['#52c41a', '#1890ff', '#722ed1', '#fa8c16'][index]}
                      size="small"
                    />
                    <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                      {formatCurrency(product.revenue)}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>

          {/* Production Stages */}
          <Col xs={24} lg={12}>
            <Card title="مراحل الإنتاج" extra={<ClockCircleOutlined />}>
              <div style={{ height: 300 }}>
                {productionStages.map((stage, index) => (
                  <div key={index} style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                      <Text>{stage.stage}</Text>
                      <Text>{stage.completed}/{stage.total}</Text>
                    </div>
                    <Progress
                      percent={stage.percentage}
                      strokeColor={stage.percentage >= 90 ? '#52c41a' : stage.percentage >= 80 ? '#fa8c16' : '#ff4d4f'}
                      size="small"
                    />
                  </div>
                ))}
              </div>
            </Card>
          </Col>

          {/* Top Clients */}
          <Col xs={24} lg={12}>
            <Card title="أفضل العملاء">
              <Table
                columns={clientColumns}
                dataSource={clientsData}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>

          {/* Key Metrics */}
          <Col xs={24}>
            <Card title="مؤشرات الأداء الرئيسية">
              <Row gutter={16}>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="متوسط قيمة الطلب"
                    value={overviewStats.averageOrderValue}
                    formatter={(value) => formatCurrency(Number(value))}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="معدل التسليم"
                    value={overviewStats.deliveryRate}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="متوسط وقت الإنتاج"
                    value={overviewStats.productionTime}
                    suffix="أيام"
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="الطلبات المعلقة"
                    value={overviewStats.pendingOrders}
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>
    </>
  )
}

export default ReportsPage
