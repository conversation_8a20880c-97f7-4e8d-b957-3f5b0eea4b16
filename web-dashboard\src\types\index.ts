// User Types
export interface User {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  role: UserRole;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  CLIENT = 'client',
  MEASUREMENT = 'measurement',
  DESIGNER = 'designer',
  CUTTING = 'cutting',
  PRESSING = 'pressing',
  VACUUM = 'vacuum',
  EDGE_CUTTING = 'edge_cutting',
  TAPE_APPLICATION = 'tape_application',
  PACKAGING = 'packaging',
  DELIVERY = 'delivery'
}

// Authentication Types
export interface LoginCredentials {
  phone_number: string;
  password?: string;
  otp?: string;
}

export interface RegisterData {
  name: string;
  phone_number: string;
  email?: string;
  password?: string;
  role?: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}
