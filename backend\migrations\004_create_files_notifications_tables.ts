import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create file_uploads table
  await knex.schema.createTable('file_uploads', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('filename', 255).notNullable();
    table.string('original_name', 255).notNullable();
    table.string('mime_type', 100).notNullable();
    table.integer('size').notNullable(); // in bytes
    table.text('url').notNullable();
    table.uuid('uploaded_by').notNullable().references('id').inTable('users');
    table.string('related_table', 50); // orders, order_items, users, etc.
    table.uuid('related_id'); // ID of the related record
    table.enum('file_type', ['image', 'document', 'design', 'measurement']).defaultTo('image');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['uploaded_by']);
    table.index(['related_table', 'related_id']);
    table.index(['file_type']);
    table.index(['created_at']);
  });

  // Create notifications table
  await knex.schema.createTable('notifications', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('title', 255).notNullable();
    table.text('message').notNullable();
    table.enum('type', ['info', 'success', 'warning', 'error']).defaultTo('info');
    table.boolean('is_read').defaultTo(false);
    table.jsonb('data'); // Additional data for the notification
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['is_read']);
    table.index(['type']);
    table.index(['created_at']);
  });

  // Create system_settings table
  await knex.schema.createTable('system_settings', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('key', 100).unique().notNullable();
    table.text('value').notNullable();
    table.string('type', 20).defaultTo('string'); // string, number, boolean, json
    table.text('description');
    table.boolean('is_public').defaultTo(false); // Can be accessed without authentication
    table.timestamps(true, true);
    
    // Indexes
    table.index(['key']);
    table.index(['is_public']);
  });

  // Create audit_logs table for tracking changes
  await knex.schema.createTable('audit_logs', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('SET NULL');
    table.string('action', 50).notNullable(); // CREATE, UPDATE, DELETE
    table.string('table_name', 50).notNullable();
    table.uuid('record_id').notNullable();
    table.jsonb('old_values'); // Previous values
    table.jsonb('new_values'); // New values
    table.inet('ip_address');
    table.text('user_agent');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['action']);
    table.index(['table_name']);
    table.index(['record_id']);
    table.index(['created_at']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('audit_logs');
  await knex.schema.dropTableIfExists('system_settings');
  await knex.schema.dropTableIfExists('notifications');
  await knex.schema.dropTableIfExists('file_uploads');
}
