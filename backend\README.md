# Al Shams Door Factory - Backend API

Backend API server for the Al Shams Door Factory Management System built with Node.js, Express, TypeScript, and PostgreSQL.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis (optional, for caching)

### Installation

```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Setup database (run migrations and seeds)
npm run db:setup

# Start development server
npm run dev
```

The API will be available at `http://localhost:3000`

## 📋 Available Scripts

### Development
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm run start        # Start production server
```

### Database
```bash
npm run db:setup     # Run migrations and seeds
npm run db:fresh     # Drop all tables and recreate (destructive)
npm run db:status    # Check migration status
npm run migrate      # Run pending migrations
npm run seed         # Run seeds
npm run cleanup      # Clean up expired data
```

### Testing & Quality
```bash
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
```

## 🗄️ Database Schema

### Core Tables
- `users` - System users (clients, technicians, admins)
- `orders` - Customer orders
- `order_items` - Individual doors within orders
- `order_stages_log` - Production stage tracking
- `colors` - Available door colors
- `patterns` - Available door patterns/designs
- `prices_config` - Pricing configuration

### Supporting Tables
- `sessions` - User session management
- `otp_codes` - Phone verification codes
- `file_uploads` - Uploaded files tracking
- `notifications` - User notifications
- `system_settings` - Application settings
- `audit_logs` - Change tracking

## 🔐 Authentication

The API uses JWT tokens for authentication:

1. **Login**: POST `/api/v1/auth/login`
2. **OTP Verification**: POST `/api/v1/auth/verify-otp`
3. **Token Refresh**: POST `/api/v1/auth/refresh`

### Default Admin Credentials
- **Phone**: +966501234567
- **Email**: <EMAIL>
- **Password**: admin123456

## 📡 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - Login with credentials
- `POST /api/v1/auth/send-otp` - Send OTP code
- `POST /api/v1/auth/verify-otp` - Verify OTP and login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - Logout

### Users
- `GET /api/v1/users` - List users (Admin)
- `GET /api/v1/users/profile` - Get current user profile
- `PUT /api/v1/users/profile` - Update profile
- `POST /api/v1/users` - Create user (Admin)
- `PUT /api/v1/users/:id` - Update user (Admin)
- `DELETE /api/v1/users/:id` - Delete user (Admin)

### Orders
- `GET /api/v1/orders` - List orders
- `GET /api/v1/orders/:id` - Get order details
- `POST /api/v1/orders` - Create new order
- `PUT /api/v1/orders/:id` - Update order
- `PUT /api/v1/orders/:id/status` - Update order status
- `DELETE /api/v1/orders/:id` - Cancel order

### Colors & Patterns
- `GET /api/v1/colors` - List colors
- `GET /api/v1/patterns` - List patterns
- `GET /api/v1/pricing` - Get pricing configuration
- `GET /api/v1/pricing/calculate` - Calculate price

### File Upload
- `POST /api/v1/upload/image` - Upload image
- `POST /api/v1/upload/document` - Upload document

### Reports
- `GET /api/v1/reports/dashboard` - Dashboard statistics
- `GET /api/v1/reports/orders` - Orders report

## 🔧 Configuration

### Environment Variables

```bash
# Server
NODE_ENV=development
PORT=3000
HOST=localhost

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=alshams_db
DB_USER=alshams_user
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# File Upload
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********

# SMS (Twilio)
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_phone_number
```

## 🏗️ Project Structure

```
backend/
├── src/
│   ├── config/          # Configuration files
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Custom middleware
│   ├── models/          # Database models
│   ├── routes/          # API routes
│   ├── services/        # Business logic
│   ├── types/           # TypeScript types
│   ├── utils/           # Helper functions
│   └── index.ts         # Application entry point
├── migrations/          # Database migrations
├── seeds/              # Database seeds
├── scripts/            # Utility scripts
├── tests/              # Test files
└── uploads/            # Uploaded files
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 📊 Monitoring

### Health Check
- `GET /health` - Application health status

### Logging
- Logs are written to `logs/` directory
- Different log levels: error, warn, info, debug
- Structured JSON logging in production

### Database Maintenance
```bash
# Clean up expired data
npm run cleanup

# Check database status
npm run db:status
```

## 🚀 Deployment

### Docker
```bash
# Build image
docker build -t alshams-backend .

# Run container
docker run -p 3000:3000 alshams-backend
```

### Production Setup
1. Set `NODE_ENV=production`
2. Use strong JWT secret
3. Configure SSL/TLS
4. Set up reverse proxy (Nginx)
5. Configure monitoring
6. Set up automated backups

## 🔒 Security Features

- JWT authentication
- Password hashing with bcrypt
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation
- SQL injection prevention
- File upload restrictions

## 📝 API Documentation

API documentation is available at `/api/v1/docs` when running in development mode.

## 🐛 Troubleshooting

### Common Issues

1. **Database connection failed**
   - Check PostgreSQL is running
   - Verify connection credentials
   - Ensure database exists

2. **Migration errors**
   - Check database permissions
   - Verify migration files syntax
   - Run `npm run db:status`

3. **File upload issues**
   - Check upload directory permissions
   - Verify file size limits
   - Check allowed file types

### Debug Mode
```bash
DEBUG=alshams:* npm run dev
```

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: `/docs`
- Health Check: `/health`
