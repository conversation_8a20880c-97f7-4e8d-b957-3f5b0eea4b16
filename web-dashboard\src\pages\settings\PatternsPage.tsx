import React, { useState } from 'react'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  Row,
  Col,
  Switch,
  message,
  Popconfirm,
  Image,
  Upload,
  InputNumber
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  AppstoreOutlined,
  UploadOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { Helmet } from 'react-helmet-async'

const { Title, Text } = Typography

interface Pattern {
  key: string
  id: string
  name: string
  name_ar: string
  image_url?: string
  base_price: number
  is_active: boolean
  created_at: string
}

const PatternsPage: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [modalVisible, setModalVisible] = useState(false)
  const [editingPattern, setEditingPattern] = useState<Pattern | null>(null)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [form] = Form.useForm()

  // دالة لتنسيق العملة العراقية
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} د.ع`
  }

  // بيانات تجريبية للنقوش
  const [patterns, setPatterns] = useState<Pattern[]>([
    {
      key: '1',
      id: '1',
      name: 'Plain',
      name_ar: 'سادة',
      base_price: 120000,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '2',
      id: '2',
      name: 'Wood Grain',
      name_ar: 'عروق خشب',
      base_price: 150000,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '3',
      id: '3',
      name: 'Modern Lines',
      name_ar: 'خطوط عصرية',
      base_price: 140000,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '4',
      id: '4',
      name: 'Classic Panel',
      name_ar: 'لوحة كلاسيكية',
      base_price: 160000,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '5',
      id: '5',
      name: 'Geometric',
      name_ar: 'هندسي',
      base_price: 145000,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '6',
      id: '6',
      name: 'Raised Panel',
      name_ar: 'لوحة مرفوعة',
      base_price: 170000,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '7',
      id: '7',
      name: 'Shaker Style',
      name_ar: 'نمط شيكر',
      base_price: 155000,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      key: '8',
      id: '8',
      name: 'Carved Design',
      name_ar: 'تصميم منحوت',
      base_price: 200000,
      is_active: false,
      created_at: '2024-01-01'
    }
  ])

  const handleAddPattern = () => {
    setEditingPattern(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEditPattern = (pattern: Pattern) => {
    setEditingPattern(pattern)
    form.setFieldsValue(pattern)
    setModalVisible(true)
  }

  const handleDeletePattern = (patternId: string) => {
    setPatterns(patterns.filter(pattern => pattern.id !== patternId))
    message.success('تم حذف النقش بنجاح')
  }

  const handleToggleStatus = (patternId: string) => {
    setPatterns(patterns.map(pattern =>
      pattern.id === patternId
        ? { ...pattern, is_active: !pattern.is_active }
        : pattern
    ))
    message.success('تم تحديث حالة النقش')
  }

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      if (editingPattern) {
        // تحديث نقش موجود
        setPatterns(patterns.map(pattern =>
          pattern.id === editingPattern.id
            ? { ...pattern, ...values }
            : pattern
        ))
        message.success('تم تحديث النقش بنجاح')
      } else {
        // إضافة نقش جديد
        const newPattern: Pattern = {
          ...values,
          key: Date.now().toString(),
          id: Date.now().toString(),
          created_at: new Date().toISOString().split('T')[0],
          is_active: true
        }
        setPatterns([...patterns, newPattern])
        message.success('تم إضافة النقش بنجاح')
      }

      setModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      title: 'النقش',
      key: 'pattern',
      render: (record: Pattern) => (
        <Space>
          <div
            style={{
              width: 60,
              height: 40,
              backgroundColor: '#f5f5f5',
              border: '2px solid #d9d9d9',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: record.image_url ? 'pointer' : 'default'
            }}
            onClick={() => {
              if (record.image_url) {
                setPreviewImage(record.image_url)
                setPreviewVisible(true)
              }
            }}
          >
            {record.image_url ? (
              <Image
                src={record.image_url}
                alt={record.name_ar}
                width={56}
                height={36}
                style={{ borderRadius: '6px' }}
                preview={false}
              />
            ) : (
              <AppstoreOutlined style={{ fontSize: 20, color: '#999' }} />
            )}
          </div>
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name_ar}</div>
            <div style={{ fontSize: 12, color: '#666' }}>
              {record.name}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: 'السعر الأساسي',
      dataIndex: 'base_price',
      key: 'base_price',
      render: (price: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          {formatCurrency(price)}
        </Text>
      ),
      sorter: (a: Pattern, b: Pattern) => a.base_price - b.base_price
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (is_active: boolean, record: Pattern) => (
        <Switch
          checked={is_active}
          onChange={() => handleToggleStatus(record.id)}
          checkedChildren="مفعل"
          unCheckedChildren="معطل"
        />
      )
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: Pattern) => (
        <Space>
          {record.image_url && (
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setPreviewImage(record.image_url!)
                setPreviewVisible(true)
              }}
            >
              معاينة
            </Button>
          )}
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditPattern(record)}
          >
            تعديل
          </Button>
          <Popconfirm
            title="هل أنت متأكد من حذف هذا النقش؟"
            description="سيؤثر هذا على جميع الطلبات المرتبطة بهذا النقش"
            onConfirm={() => handleDeletePattern(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              حذف
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  const filteredPatterns = patterns.filter(pattern => {
    const matchesSearch = !searchText ||
      pattern.name.toLowerCase().includes(searchText.toLowerCase()) ||
      pattern.name_ar.toLowerCase().includes(searchText.toLowerCase())

    return matchesSearch
  })

  return (
    <>
      <Helmet>
        <title>إدارة النقوش - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={2} style={{ margin: 0 }}>
              إدارة النقوش
            </Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddPattern}
            >
              نقش جديد
            </Button>
          </div>

          {/* Search */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16} align="middle">
              <Col xs={24} sm={12}>
                <Input
                  placeholder="البحث في النقوش..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
            </Row>
          </Card>
        </div>

        <Card>
          <Table
            columns={columns}
            dataSource={filteredPatterns}
            loading={loading}
            pagination={{
              total: filteredPatterns.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} من ${total} نقش`
            }}
          />
        </Card>

        {/* Modal for Add/Edit Pattern */}
        <Modal
          title={editingPattern ? 'تعديل النقش' : 'إضافة نقش جديد'}
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={null}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Row gutter={16}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="name_ar"
                  label="الاسم بالعربية"
                  rules={[{ required: true, message: 'يرجى إدخال الاسم بالعربية' }]}
                >
                  <Input
                    placeholder="أدخل الاسم بالعربية"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="name"
                  label="الاسم بالإنجليزية"
                  rules={[{ required: true, message: 'يرجى إدخال الاسم بالإنجليزية' }]}
                >
                  <Input
                    placeholder="Enter English name"
                    dir="ltr"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="base_price"
                  label="السعر الأساسي (د.ع)"
                  rules={[
                    { required: true, message: 'يرجى إدخال السعر الأساسي' },
                    { type: 'number', min: 1000, message: 'السعر يجب أن يكون أكبر من 1000' }
                  ]}
                >
                  <InputNumber
                    placeholder="120000"
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                    min={1000}
                    max={1000000}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="image_url"
                  label="رابط الصورة (اختياري)"
                >
                  <Input
                    placeholder="https://example.com/pattern.jpg"
                    dir="ltr"
                    addonAfter={<UploadOutlined />}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Preview */}
            <Form.Item noStyle shouldUpdate>
              {() => {
                const nameAr = form.getFieldValue('name_ar')
                const name = form.getFieldValue('name')
                const basePrice = form.getFieldValue('base_price')
                const imageUrl = form.getFieldValue('image_url')

                if (nameAr && basePrice) {
                  return (
                    <Card
                      type="inner"
                      title="معاينة النقش"
                      style={{ marginBottom: 16 }}
                    >
                      <Space>
                        <div
                          style={{
                            width: 80,
                            height: 60,
                            backgroundColor: '#f5f5f5',
                            border: '2px solid #d9d9d9',
                            borderRadius: '8px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          {imageUrl ? (
                            <Image
                              src={imageUrl}
                              alt={nameAr}
                              width={76}
                              height={56}
                              style={{ borderRadius: '6px' }}
                              preview={false}
                            />
                          ) : (
                            <AppstoreOutlined style={{ fontSize: 24, color: '#999' }} />
                          )}
                        </div>
                        <div>
                          <div style={{ fontWeight: 'bold', fontSize: 16 }}>{nameAr}</div>
                          <div style={{ color: '#666' }}>{name}</div>
                          <div style={{
                            color: '#1890ff',
                            fontWeight: 'bold',
                            fontSize: 14
                          }}>
                            {formatCurrency(basePrice)}
                          </div>
                        </div>
                      </Space>
                    </Card>
                  )
                }
                return null
              }}
            </Form.Item>

            <div style={{ textAlign: 'left', marginTop: 24 }}>
              <Space>
                <Button onClick={() => setModalVisible(false)}>
                  إلغاء
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<AppstoreOutlined />}
                >
                  {editingPattern ? 'تحديث' : 'إضافة'}
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>

        {/* Image Preview Modal */}
        <Modal
          open={previewVisible}
          title="معاينة النقش"
          footer={null}
          onCancel={() => setPreviewVisible(false)}
        >
          <Image
            alt="Pattern preview"
            style={{ width: '100%' }}
            src={previewImage}
          />
        </Modal>
      </div>
    </>
  )
}

export default PatternsPage
