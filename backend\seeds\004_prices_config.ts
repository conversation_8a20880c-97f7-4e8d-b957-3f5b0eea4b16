import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Delete existing price configurations
  await knex('prices_config').del();

  // Get all colors and patterns
  const colors = await knex('colors').select('id', 'name');
  const patterns = await knex('patterns').select('id', 'name');

  // Define base prices for different pattern types
  const patternPrices: Record<string, number> = {
    'Plain': 120.00,
    'Wood Grain': 150.00,
    'Modern Lines': 140.00,
    'Classic Panel': 160.00,
    'Geometric': 145.00,
    'Raised Panel': 170.00,
    'Shaker Style': 155.00,
    'Traditional': 165.00,
    'Contemporary': 150.00,
    'Minimalist': 135.00,
    'Carved Design': 200.00,
    'Glass Insert': 180.00
  };

  // Define color multipliers
  const colorMultipliers: Record<string, number> = {
    'White': 1.0,
    'Cream': 1.05,
    'Light Brown': 1.1,
    'Brown': 1.15,
    'Dark Brown': 1.2,
    'Black': 1.25,
    'Gray': 1.1,
    'Light Gray': 1.05,
    'Beige': 1.05,
    'Mahogany': 1.3,
    'Oak': 1.25,
    'Cherry': 1.35
  };

  const priceConfigs = [];

  // Generate price configurations for all color-pattern combinations
  for (const color of colors) {
    for (const pattern of patterns) {
      const basePrice = patternPrices[pattern.name] || 150.00;
      const colorMultiplier = colorMultipliers[color.name] || 1.0;
      const finalPrice = Math.round(basePrice * colorMultiplier * 100) / 100;

      priceConfigs.push({
        id: knex.raw('gen_random_uuid()'),
        color_id: color.id,
        pattern_id: pattern.id,
        base_price_per_m2: finalPrice,
        last_updated: knex.fn.now(),
        created_at: knex.fn.now(),
        updated_at: knex.fn.now()
      });
    }
  }

  // Insert price configurations in batches
  const batchSize = 50;
  for (let i = 0; i < priceConfigs.length; i += batchSize) {
    const batch = priceConfigs.slice(i, i + batchSize);
    await knex('prices_config').insert(batch);
  }

  console.log(`✅ Inserted ${priceConfigs.length} price configurations successfully`);
  console.log(`📊 Price range: ${Math.min(...priceConfigs.map(p => p.base_price_per_m2))} - ${Math.max(...priceConfigs.map(p => p.base_price_per_m2))} SAR per m²`);
}
