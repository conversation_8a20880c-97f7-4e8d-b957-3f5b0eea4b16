import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Typography,
  Space,
  But<PERSON>,
  Badge,
  Drawer
} from 'antd'
import {
  DashboardOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  SettingOutlined,
  Bar<PERSON>hartOutlined,
  LogoutOutlined,
  MenuOutlined,
  BellOutlined,
  ShopOutlined,
  TeamOutlined,
  BgColorsOutlined,
  AppstoreOutlined,
  DollarOutlined
} from '@ant-design/icons'

import { useAuthStore, useUser } from '../stores/authStore'
import { UserRole } from '@/types'

const { Header, Sider, Content } = Layout
const { Text } = Typography

const DashboardLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false)
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { logout } = useAuthStore()
  const user = useUser()

  const isMobile = window.innerWidth <= 768

  const handleLogout = async () => {
    try {
      await logout()
      navigate('/auth/login')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'الملف الشخصي',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'الإعدادات',
      onClick: () => navigate('/settings')
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'تسجيل الخروج',
      onClick: handleLogout
    }
  ]

  const getMenuItems = () => {
    const baseItems = [
      {
        key: '/dashboard',
        icon: <DashboardOutlined />,
        label: 'لوحة التحكم'
      }
    ]

    // Orders menu for all users
    const ordersItems = [
      {
        key: '/orders',
        icon: <ShoppingCartOutlined />,
        label: 'الطلبات'
      }
    ]

    // Admin and management items
    const adminItems = user?.role === UserRole.ADMIN ? [
      {
        key: '/users',
        icon: <TeamOutlined />,
        label: 'المستخدمين'
      },
      {
        key: 'settings',
        icon: <SettingOutlined />,
        label: 'الإعدادات',
        children: [
          {
            key: '/settings/colors',
            icon: <BgColorsOutlined />,
            label: 'الألوان'
          },
          {
            key: '/settings/patterns',
            icon: <AppstoreOutlined />,
            label: 'النقوش'
          },
          {
            key: '/settings/pricing',
            icon: <DollarOutlined />,
            label: 'الأسعار'
          }
        ]
      },
      {
        key: '/reports',
        icon: <BarChartOutlined />,
        label: 'التقارير'
      }
    ] : []

    return [...baseItems, ...ordersItems, ...adminItems]
  }

  const menuItems = getMenuItems()

  const siderContent = (
    <>
      <div style={{
        height: 64,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'rgba(255, 255, 255, 0.1)',
        margin: '16px',
        borderRadius: '8px'
      }}>
        <Space>
          <ShopOutlined style={{ fontSize: 24, color: 'white' }} />
          {!collapsed && (
            <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>
              معمل الشمس
            </Text>
          )}
        </Space>
      </div>

      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={({ key }) => {
          navigate(key)
          if (isMobile) {
            setMobileMenuVisible(false)
          }
        }}
        style={{ border: 'none' }}
      />
    </>
  )

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Desktop Sidebar */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          style={{
            background: '#001529'
          }}
        >
          {siderContent}
        </Sider>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Drawer
          title={
            <Space>
              <ShopOutlined style={{ color: '#1890ff' }} />
              <Text strong>معمل الشمس</Text>
            </Space>
          }
          placement="right"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          bodyStyle={{ padding: 0, background: '#001529' }}
          headerStyle={{ background: '#001529', borderBottom: '1px solid #303030' }}
        >
          <div style={{ background: '#001529', minHeight: '100%' }}>
            {siderContent}
          </div>
        </Drawer>
      )}

      <Layout>
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <Space>
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => {
                if (isMobile) {
                  setMobileMenuVisible(true)
                } else {
                  setCollapsed(!collapsed)
                }
              }}
              style={{ fontSize: 16 }}
            />
          </Space>

          <Space size="middle">
            <Badge count={0} showZero={false}>
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: 16 }}
              />
            </Badge>

            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomLeft"
              trigger={['click']}
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
                <div style={{ textAlign: 'right' }}>
                  <div style={{ fontSize: 14, fontWeight: 500 }}>
                    {user?.name}
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    {user?.role === UserRole.ADMIN ? 'مدير' :
                     user?.role === UserRole.CLIENT ? 'عميل' :
                     user?.role === UserRole.MEASUREMENT ? 'قياسات' :
                     user?.role === UserRole.DESIGNER ? 'مصمم' :
                     user?.role === UserRole.CUTTING ? 'تفصال' :
                     user?.role === UserRole.PRESSING ? 'كبس' :
                     user?.role === UserRole.VACUUM ? 'فاكيوم' :
                     user?.role === UserRole.EDGE_CUTTING ? 'حواف' :
                     user?.role === UserRole.TAPE_APPLICATION ? 'شريط' :
                     user?.role === UserRole.PACKAGING ? 'تغليف' :
                     user?.role === UserRole.DELIVERY ? 'توصيل' : 'مستخدم'}
                  </div>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        <Content style={{
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)',
          overflow: 'auto'
        }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  )
}

export default DashboardLayout
