import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create orders table
  await knex.schema.createTable('orders', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('client_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.enum('status', [
      'new',
      'awaiting_measurement',
      'in_design',
      'in_cutting',
      'in_pressing',
      'in_vacuum',
      'edge_cutting',
      'tape_application',
      'packaging',
      'ready_for_delivery',
      'delivered',
      'cancelled'
    ]).notNullable().defaultTo('new');
    table.text('location').notNullable();
    table.jsonb('location_coordinates'); // {latitude: number, longitude: number}
    table.decimal('total_price', 10, 2).defaultTo(0);
    table.text('notes');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['client_id']);
    table.index(['status']);
    table.index(['created_at']);
    table.index(['total_price']);
  });

  // Create order_items table (individual doors)
  await knex.schema.createTable('order_items', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('order_id').notNullable().references('id').inTable('orders').onDelete('CASCADE');
    table.decimal('width', 5, 2).notNullable(); // in cm
    table.decimal('height', 5, 2).notNullable(); // in cm
    table.uuid('color_id').notNullable().references('id').inTable('colors');
    table.uuid('pattern_id').notNullable().references('id').inTable('patterns');
    table.string('opening_type', 100); // نوع الفتحة
    table.text('preview_image'); // URL to preview image
    table.decimal('price', 8, 2).notNullable();
    table.text('notes');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['order_id']);
    table.index(['color_id']);
    table.index(['pattern_id']);
    table.index(['price']);
  });

  // Create order_stages_log table
  await knex.schema.createTable('order_stages_log', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('order_item_id').notNullable().references('id').inTable('order_items').onDelete('CASCADE');
    table.enum('stage_name', [
      'new',
      'awaiting_measurement',
      'in_design',
      'in_cutting',
      'in_pressing',
      'in_vacuum',
      'edge_cutting',
      'tape_application',
      'packaging',
      'ready_for_delivery',
      'delivered',
      'cancelled'
    ]).notNullable();
    table.uuid('technician_id').notNullable().references('id').inTable('users');
    table.timestamp('start_time').notNullable().defaultTo(knex.fn.now());
    table.timestamp('end_time');
    table.text('notes');
    table.text('photo_url'); // URL to uploaded photo
    table.timestamps(true, true);
    
    // Indexes
    table.index(['order_item_id']);
    table.index(['technician_id']);
    table.index(['stage_name']);
    table.index(['start_time']);
    table.index(['end_time']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('order_stages_log');
  await knex.schema.dropTableIfExists('order_items');
  await knex.schema.dropTableIfExists('orders');
}
