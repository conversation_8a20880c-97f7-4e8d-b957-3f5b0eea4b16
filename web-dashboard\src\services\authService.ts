import { apiClient } from './apiClient'
import { User, LoginCredentials, RegisterData, AuthTokens } from '../types'

export interface LoginResponse {
  user: User
  access_token: string
  refresh_token: string
  expires_in: number
}

export interface RefreshTokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
}

export interface SendOTPRequest {
  phone_number: string
}

export interface VerifyOTPRequest {
  phone_number: string
  otp: string
}

class AuthService {
  /**
   * Login with phone and password
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/login', credentials)
    return response.data
  }

  /**
   * Register new user
   */
  async register(userData: RegisterData): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/register', userData)
    return response.data
  }

  /**
   * Send OTP to phone number
   */
  async sendOTP(data: SendOTPRequest): Promise<{ message: string }> {
    const response = await apiClient.post('/auth/send-otp', data)
    return response.data
  }

  /**
   * Verify OTP and login
   */
  async verifyOTP(data: VerifyOTPRequest): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/verify-otp', data)
    return response.data
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    const response = await apiClient.post<RefreshTokenResponse>('/auth/refresh', {
      refresh_token: refreshToken
    })
    return response.data
  }

  /**
   * Logout user
   */
  async logout(refreshToken: string): Promise<void> {
    await apiClient.post('/auth/logout', {
      refresh_token: refreshToken
    })
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<User> {
    const response = await apiClient.get<User>('/users/profile')
    return response.data
  }

  /**
   * Update user profile
   */
  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>('/users/profile', userData)
    return response.data
  }

  /**
   * Change password
   */
  async changePassword(data: {
    current_password: string
    new_password: string
    confirm_password: string
  }): Promise<{ message: string }> {
    const response = await apiClient.put('/users/change-password', data)
    return response.data
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(phone_number: string): Promise<{ message: string }> {
    const response = await apiClient.post('/auth/forgot-password', { phone_number })
    return response.data
  }

  /**
   * Reset password with OTP
   */
  async resetPassword(data: {
    phone_number: string
    otp: string
    new_password: string
    confirm_password: string
  }): Promise<{ message: string }> {
    const response = await apiClient.post('/auth/reset-password', data)
    return response.data
  }

  /**
   * Check if phone number is available
   */
  async checkPhoneAvailability(phone_number: string): Promise<{ available: boolean }> {
    const response = await apiClient.get(`/auth/check-phone?phone_number=${phone_number}`)
    return response.data
  }

  /**
   * Check if email is available
   */
  async checkEmailAvailability(email: string): Promise<{ available: boolean }> {
    const response = await apiClient.get(`/auth/check-email?email=${email}`)
    return response.data
  }
}

export const authService = new AuthService()
