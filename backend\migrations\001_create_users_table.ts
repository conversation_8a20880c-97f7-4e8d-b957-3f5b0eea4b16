import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create users table
  await knex.schema.createTable('users', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 100).notNullable();
    table.string('phone_number', 20).unique().notNullable();
    table.string('email', 255).unique();
    table.string('password_hash', 255);
    table.enum('role', [
      'admin',
      'client', 
      'measurement',
      'designer',
      'cutting',
      'pressing',
      'vacuum',
      'edge_cutting',
      'tape_application',
      'packaging',
      'delivery'
    ]).notNullable();
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_login');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['phone_number']);
    table.index(['email']);
    table.index(['role']);
    table.index(['is_active']);
  });

  // Create sessions table for refresh tokens
  await knex.schema.createTable('sessions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.text('refresh_token').notNullable();
    table.jsonb('device_info');
    table.inet('ip_address');
    table.timestamp('expires_at').notNullable();
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['expires_at']);
    table.index(['refresh_token']);
  });

  // Create OTP table for phone verification
  await knex.schema.createTable('otp_codes', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('phone_number', 20).notNullable();
    table.string('code', 6).notNullable();
    table.timestamp('expires_at').notNullable();
    table.integer('attempts').defaultTo(0);
    table.boolean('is_used').defaultTo(false);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['phone_number']);
    table.index(['expires_at']);
    table.index(['code']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('otp_codes');
  await knex.schema.dropTableIfExists('sessions');
  await knex.schema.dropTableIfExists('users');
}
