import React from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Button, Table, Tag, Progress } from 'antd'
import {
  ShoppingCartOutlined,
  UserOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  TruckOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'

const { Title, Text } = Typography

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()

  // Mock data - في التطبيق الحقيقي ستأتي من API
  const stats = {
    totalOrders: 156,
    pendingOrders: 23,
    completedOrders: 133,
    totalRevenue: 245000,
    todayOrders: 8,
    inProgress: 15
  }

  const recentOrders = [
    {
      key: '1',
      id: 'ORD-001',
      client: 'أحمد محمد',
      status: 'in_design',
      items: 2,
      total: 1500,
      date: '2024-01-15'
    },
    {
      key: '2',
      id: 'ORD-002',
      client: 'فاطمة السعد',
      status: 'in_cutting',
      items: 1,
      total: 800,
      date: '2024-01-14'
    },
    {
      key: '3',
      id: 'ORD-003',
      client: 'محمد العلي',
      status: 'ready_for_delivery',
      items: 3,
      total: 2200,
      date: '2024-01-13'
    }
  ]

  const getStatusTag = (status: string) => {
    const statusMap = {
      'new': { color: 'blue', text: 'طلب جديد' },
      'in_design': { color: 'purple', text: 'في التصميم' },
      'in_cutting': { color: 'orange', text: 'في التفصال' },
      'in_pressing': { color: 'cyan', text: 'في الكبس' },
      'ready_for_delivery': { color: 'green', text: 'جاهز للتوصيل' },
      'delivered': { color: 'success', text: 'تم التوصيل' }
    }
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
  }

  const orderColumns = [
    {
      title: 'رقم الطلب',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'العميل',
      dataIndex: 'client',
      key: 'client'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: 'عدد الأبواب',
      dataIndex: 'items',
      key: 'items'
    },
    {
      title: 'المبلغ',
      dataIndex: 'total',
      key: 'total',
      render: (amount: number) => `${amount.toLocaleString()} ريال`
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: any) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/orders/${record.key}`)}
        >
          عرض
        </Button>
      )
    }
  ]

  const productionStages = [
    { name: 'القياسات', completed: 8, total: 10, color: '#52c41a' },
    { name: 'التصميم', completed: 6, total: 8, color: '#1890ff' },
    { name: 'التفصال', completed: 5, total: 6, color: '#fa8c16' },
    { name: 'الكبس', completed: 4, total: 5, color: '#eb2f96' },
    { name: 'التغليف', completed: 3, total: 4, color: '#722ed1' }
  ]

  return (
    <>
      <Helmet>
        <title>لوحة التحكم - معمل الشمس</title>
      </Helmet>

      <div>
        <div style={{ marginBottom: 24 }}>
          <Title level={2} style={{ margin: 0 }}>
            لوحة التحكم
          </Title>
          <Text type="secondary">
            نظرة عامة على أداء المعمل والطلبات الحالية
          </Text>
        </div>

        {/* Statistics Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="إجمالي الطلبات"
                value={stats.totalOrders}
                prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="طلبات قيد التنفيذ"
                value={stats.inProgress}
                prefix={<ClockCircleOutlined style={{ color: '#fa8c16' }} />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="طلبات مكتملة"
                value={stats.completedOrders}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="إجمالي الإيرادات"
                value={stats.totalRevenue}
                prefix={<DollarOutlined style={{ color: '#722ed1' }} />}
                suffix="ريال"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* Recent Orders */}
          <Col xs={24} lg={16}>
            <Card
              title="الطلبات الأخيرة"
              extra={
                <Button type="primary" onClick={() => navigate('/orders')}>
                  عرض جميع الطلبات
                </Button>
              }
            >
              <Table
                columns={orderColumns}
                dataSource={recentOrders}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>

          {/* Production Progress */}
          <Col xs={24} lg={8}>
            <Card title="تقدم الإنتاج" style={{ height: '100%' }}>
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                {productionStages.map((stage, index) => (
                  <div key={index}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                      <Text>{stage.name}</Text>
                      <Text type="secondary">{stage.completed}/{stage.total}</Text>
                    </div>
                    <Progress
                      percent={Math.round((stage.completed / stage.total) * 100)}
                      strokeColor={stage.color}
                      size="small"
                    />
                  </div>
                ))}
              </Space>
            </Card>
          </Col>
        </Row>

        {/* Quick Actions */}
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="إجراءات سريعة">
              <Space wrap size="middle">
                <Button
                  type="primary"
                  icon={<ShoppingCartOutlined />}
                  onClick={() => navigate('/orders/create')}
                >
                  طلب جديد
                </Button>
                <Button
                  icon={<UserOutlined />}
                  onClick={() => navigate('/users')}
                >
                  إدارة المستخدمين
                </Button>
                <Button
                  icon={<ToolOutlined />}
                  onClick={() => navigate('/settings')}
                >
                  الإعدادات
                </Button>
                <Button
                  icon={<TruckOutlined />}
                  onClick={() => navigate('/orders?status=ready_for_delivery')}
                >
                  طلبات جاهزة للتوصيل
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    </>
  )
}

export default DashboardPage
