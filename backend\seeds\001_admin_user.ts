import { Knex } from 'knex';
import bcrypt from 'bcryptjs';

export async function seed(knex: Knex): Promise<void> {
  // Delete existing admin users
  await knex('users').where('role', 'admin').del();

  // Hash password for admin user
  const passwordHash = await bcrypt.hash('admin123456', 12);

  // Insert admin user
  await knex('users').insert([
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'مدير النظام',
      phone_number: '+************',
      email: '<EMAIL>',
      password_hash: passwordHash,
      role: 'admin',
      is_active: true,
      created_at: knex.fn.now(),
      updated_at: knex.fn.now()
    }
  ]);

  console.log('✅ Admin user created successfully');
  console.log('📱 Phone: +************');
  console.log('📧 Email: <EMAIL>');
  console.log('🔑 Password: admin123456');
}
